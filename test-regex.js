// Test V2 regex patterns
const REGEX_V2_GLUE_THICKNESS = /INFO\s+.*Thickness:([-\d.]+)/;
const REGEX_V2_COORDS = /点13均值x:([-\d.]+),\s*点13均值y:([-\d.]+),\s*点2x:([-\d.]+),\s*点2y:([-\d.]+)/;

// Test lines from v2.txt
const testLines = [
    "INFO  2025-07-14 13:07:35,070 Thickness:922.879699707031 peak1:0 peak2:0 len:80",
    "INFO  2025-07-14 13:07:35,070 点13均值x:499999999.50312, 点13均值y:499999999.477124, 点2x:0.166964, 点2y:-0.064603",
    "INFO  2025-07-14 13:07:35,244 点13均值x:-0.001171, 点13均值y:-0.0386575, 点2x:0.168375, 点2y:-0.062395"
];

console.log('=== Testing V2 Regex Patterns ===');

testLines.forEach((line, index) => {
    console.log(`\nTest line ${index + 1}: ${line}`);
    
    const glueMatch = line.match(REGEX_V2_GLUE_THICKNESS);
    if (glueMatch) {
        console.log(`  Glue thickness match: ${glueMatch[1]}`);
    } else {
        console.log(`  No glue thickness match`);
    }
    
    const coordsMatch = line.match(REGEX_V2_COORDS);
    if (coordsMatch) {
        console.log(`  Coords match: x1=${coordsMatch[1]}, y1=${coordsMatch[2]}, x2=${coordsMatch[3]}, y2=${coordsMatch[4]}`);
    } else {
        console.log(`  No coords match`);
    }
});
