"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.module.ts":
/*!*************************************!*\
  !*** ./workers/logParser.module.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findBlocksByTimestamp: () => (/* binding */ findBlocksByTimestamp),\n/* harmony export */   processLogFile: () => (/* binding */ processLogFile)\n/* harmony export */ });\n/* harmony import */ var _dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataExtractor.module */ \"(app-pages-browser)/./workers/dataExtractor.module.ts\");\n\n/**\r\n * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。\r\n * @param logContent 完整的日志内容字符串。\r\n * @returns 处理后的日志结果。\r\n */ function processLogFile(logContent) {\n    const lines = logContent.split(/\\r?\\n/);\n    const results = [];\n    let v1Count = 0;\n    let v2Count = 0;\n    let unknownCount = 0;\n    let endOfBlock = -1;\n    for(let i = lines.length - 1; i >= 0; i--){\n        // V1 and V2 blocks end with 'insert into g_support'\n        if (lines[i].includes('insert into g_support')) {\n            endOfBlock = i;\n            let startOfBlock = -1;\n            for(let j = endOfBlock; j >= 0; j--){\n                // V1 and V2 blocks start with '开始抽真空'\n                if (j < 10) {\n                    console.log(j);\n                    console.log(j);\n                }\n                if (lines[j].includes('开始抽真空')) {\n                    startOfBlock = j;\n                    break;\n                }\n            }\n            if (startOfBlock !== -1) {\n                const blockLines = lines.slice(startOfBlock, endOfBlock + 1);\n                const processedBlock = (0,_dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__.extractDataFromBlock)(blockLines);\n                results.push(processedBlock);\n                // Simple version detection for statistics\n                // This can be improved if more specific rules are available\n                if (processedBlock.version === 'V1') {\n                    v1Count++;\n                } else if (processedBlock.version === 'V2') {\n                    v2Count++;\n                } else {\n                    unknownCount++;\n                }\n                // Move the index to the beginning of the found block to avoid re-processing\n                i = startOfBlock;\n            }\n        }\n    }\n    // Since we are iterating backwards, reverse the results to restore chronological order\n    results.reverse();\n    return {\n        blocks: results,\n        totalLines: lines.length,\n        v1Count,\n        v2Count,\n        unknownCount\n    };\n}\n/**\r\n * Finds block IDs that match a given list of timestamps.\r\n * @param blocks The array of processed log blocks.\r\n * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.\r\n * @returns An array of block_ids that match the timestamps.\r\n */ function findBlocksByTimestamp(blocks, timestampsToMatch) {\n    const timestampSet = new Set(timestampsToMatch);\n    const matchedBlockIds = [];\n    for (const block of blocks){\n        if (block.start_time) {\n            try {\n                // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'\n                const date = new Date(block.start_time.replace(',', '.'));\n                const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);\n                if (timestampSet.has(blockTimestampInSeconds)) {\n                    matchedBlockIds.push(block.block_id);\n                }\n            } catch (e) {\n                console.error(\"Error parsing date for block \".concat(block.block_id, \":\"), e);\n            }\n        }\n    }\n    return matchedBlockIds;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.module.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("111a2fe11da23f04")
/******/ })();
/******/ 
/******/ }
);