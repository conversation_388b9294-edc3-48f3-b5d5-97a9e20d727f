"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.module.ts":
/*!*************************************!*\
  !*** ./workers/logParser.module.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findBlocksByTimestamp: () => (/* binding */ findBlocksByTimestamp),\n/* harmony export */   processLogFile: () => (/* binding */ processLogFile)\n/* harmony export */ });\n/* harmony import */ var _dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataExtractor.module */ \"(app-pages-browser)/./workers/dataExtractor.module.ts\");\n\n/**\r\n * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。\r\n * @param logContent 完整的日志内容字符串。\r\n * @returns 处理后的日志结果。\r\n */ function processLogFile(logContent) {\n    const lines = logContent.split(/\\r?\\n/);\n    const results = [];\n    let v1Count = 0;\n    let v2Count = 0;\n    let unknownCount = 0;\n    let endOfBlock = -1;\n    console.log(111);\n    for(let i = lines.length - 1; i >= 0; i--){\n        // V1 and V2 blocks end with 'insert into g_support'\n        if (lines[i].includes('insert into g_support')) {\n            endOfBlock = i;\n            let startOfBlock = -1;\n            for(let j = endOfBlock; j >= 0; j--){\n                // V1 and V2 blocks start with '开始抽真空'\n                if (lines[j].includes('开始抽真空')) {\n                    startOfBlock = j;\n                    break;\n                }\n            }\n            if (startOfBlock !== -1) {\n                const blockLines = lines.slice(startOfBlock, endOfBlock + 1);\n                const processedBlock = (0,_dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__.extractDataFromBlock)(blockLines);\n                results.push(processedBlock);\n                // Simple version detection for statistics\n                // This can be improved if more specific rules are available\n                if (processedBlock.version === 'V1') {\n                    v1Count++;\n                } else if (processedBlock.version === 'V2') {\n                    v2Count++;\n                } else {\n                    unknownCount++;\n                }\n                // Move the index to the beginning of the found block to avoid re-processing\n                i = startOfBlock;\n            }\n        }\n    }\n    // Since we are iterating backwards, reverse the results to restore chronological order\n    results.reverse();\n    return {\n        blocks: results,\n        totalLines: lines.length,\n        v1Count,\n        v2Count,\n        unknownCount\n    };\n}\n/**\r\n * Finds block IDs that match a given list of timestamps.\r\n * @param blocks The array of processed log blocks.\r\n * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.\r\n * @returns An array of block_ids that match the timestamps.\r\n */ function findBlocksByTimestamp(blocks, timestampsToMatch) {\n    const timestampSet = new Set(timestampsToMatch);\n    const matchedBlockIds = [];\n    for (const block of blocks){\n        if (block.start_time) {\n            try {\n                // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'\n                const date = new Date(block.start_time.replace(',', '.'));\n                const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);\n                if (timestampSet.has(blockTimestampInSeconds)) {\n                    matchedBlockIds.push(block.block_id);\n                }\n            } catch (e) {\n                console.error(\"Error parsing date for block \".concat(block.block_id, \":\"), e);\n            }\n        }\n    }\n    return matchedBlockIds;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.module.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("a1f86feba9773b37")
/******/ })();
/******/ 
/******/ }
);