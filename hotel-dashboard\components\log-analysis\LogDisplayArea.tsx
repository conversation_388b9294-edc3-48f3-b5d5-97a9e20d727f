"use client";

import React, { useState, useEffect, useCallback, useRef } from 'react';
// LogChartView is removed as it will be handled by the parent page
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';

import { useToast } from '@/components/ui/use-toast';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';

import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { LogChartView } from './LogChartView';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogDisplayAreaProps {
  dataChunks: ProcessedBlock[];
  selectedBlockIds: Set<string>;
  onSelectionChange: (selectedIds: Set<string>) => void;
  onStartExport: (exportIds: string[]) => void;
  onExportXlsx: () => void;
  highlightedBlockIds?: Set<string>;
  isSearching?: boolean;
}

const LogDisplayArea: React.FC<LogDisplayAreaProps> = ({
  dataChunks,
  selectedBlockIds,
  onSelectionChange,
  onStartExport,
  onExportXlsx,
  highlightedBlockIds = new Set(),
  isSearching = false
}) => {
  const displayAreaRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  const handleSelectionChange = useCallback((blockId: string) => {
    const newSelection = new Set(selectedBlockIds);
    if (newSelection.has(blockId)) {
      newSelection.delete(blockId);
    } else {
      newSelection.add(blockId);
    }
    onSelectionChange(newSelection);
  }, [selectedBlockIds, onSelectionChange]);

  const selectAll = useCallback(() => {
    const allIds = new Set(dataChunks.map(chunk => chunk.block_id));
    onSelectionChange(allIds);
  }, [dataChunks, onSelectionChange]);

  const deselectAll = useCallback(() => {
    onSelectionChange(new Set());
  }, [onSelectionChange]);

  const handleExport = () => {
    if (selectedBlockIds.size === 0) {
      toast({
        variant: 'destructive',
        title: '错误',
        description: '请至少选择一个数据块进行导出。'
      });
      return;
    }
    onStartExport(Array.from(selectedBlockIds));
  };

  const hasContentToExport = dataChunks && dataChunks.length > 0;

  return (
    <Card className="h-[450px] flex flex-col">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>选择数据块</CardTitle>
          <CardDescription>
            选择数据块以进行图表分析或导出。
          </CardDescription>
        </div>
        <div className="flex gap-2">
          <Button onClick={onExportXlsx} disabled={!hasContentToExport || selectedBlockIds.size === 0} size="sm" variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出为 XLSX
          </Button>
          <Button onClick={handleExport} disabled={!hasContentToExport || selectedBlockIds.size === 0} size="sm">
            <Download className="mr-2 h-4 w-4" />
            导出选中图片 ({selectedBlockIds.size})
          </Button>
        </div>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-4">
        <div ref={displayAreaRef} className="h-full flex flex-col">
          <div className="flex items-center space-x-2 mb-4">
            <Button onClick={selectAll} size="sm" variant="outline">全选</Button>
            <Button onClick={deselectAll} size="sm" variant="outline">取消全选</Button>
            <span className="text-sm text-muted-foreground ml-2">已选择: {selectedBlockIds.size} 项</span>
          </div>
          <div className="flex-1 overflow-y-auto min-h-0">
            <div className="space-y-2 p-2 border rounded-md">
              {dataChunks.length === 0 && isSearching && (
                <p className="text-muted-foreground p-2">在当前筛选结果中无数据块。</p>
              )}
              {dataChunks.map((chunk) => (
                <div
                  key={chunk.block_id}
                  className={`flex items-center space-x-4 p-2 rounded-md transition-colors ${highlightedBlockIds.has(chunk.block_id) ? 'bg-blue-100 dark:bg-blue-900/30' : ''}`}
                >
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id={`select-${chunk.block_id}`}
                      checked={selectedBlockIds.has(chunk.block_id)}
                      onCheckedChange={() => handleSelectionChange(chunk.block_id)}
                    />
                    <Label htmlFor={`select-${chunk.block_id}`} className="cursor-pointer">
                      {`数据块 ${chunk.block_id} (胶厚: ${chunk.glue_thickness_values.length}, 准直: ${chunk.collimation_diff_values.length})`}
                    </Label>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {(!dataChunks || dataChunks.length === 0) && (
            <p className="text-muted-foreground p-2">暂无数据块可供分析或导出。</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LogDisplayArea;