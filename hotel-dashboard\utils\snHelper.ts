/**
 * 将包含多个SN（序列号）的原始输入字符串解析为唯一的SN数组。
 *
 * 此函数设计用于处理用户输入的、可能包含多种分隔符（如逗号、分号、空格或换行符）的SN列表。
 * 它会规范化输入，分割字符串，去除空白，并确保返回的数组中不包含重复的SN。
 *
 * @param input - 用户输入的原始字符串，例如 "SN001, SN002; SN003 SN001"。
 * @returns 返回一个经过清理和去重的SN字符串数组。如果输入为空或只包含空白，则返回一个空数组。
 * @example
 * ```ts
 * const input = "SN001, SN002; SN003 SN001";
 * const result = parseSnInput(input);
 * // result is ["SN001", "SN002", "SN003"]
 * ```
 */
export const parseSnInput = (input: string): string[] => {
  if (!input || input.trim() === "") {
    return [];
  }

  // Replace commas and semicolons with spaces to unify delimiters
  const normalizedInput = input.replace(/[,;]/g, " ");

  // Split by one or more spaces, then trim and filter out empty strings
  const sns = normalizedInput
    .split(/\s+/)
    .map(sn => sn.trim())
    .filter(sn => sn.length > 0);

  // Return a unique set of SNs
  return Array.from(new Set(sns));
};
/**
 * 从给定的文本中查找并提取第一个匹配的SN（序列号）。
 *
 * 此函数通过正则表达式搜索 `insert into g_support` 或 `insert into h_support` 的SQL语句，
 * 并从该语句的 `values` 子句中提取第一个值作为SN。
 * 设计用于从日志数据块中快速定位与数据库记录相关的SN。
 *
 * @param text - 需要在其中搜索SN的文本内容，通常是一段日志。
 * @returns 如果找到匹配项，则返回提取到的SN字符串；否则返回 `null`。
 * @example
 * ```ts
 * const logLine = '... insert into g_support(sn, ...) values("GBSN12345", ...);';
 * const sn = findSN(logLine);
 * // sn is "GBSN12345"
 *
 * const logLineH = '... insert into h_support(sn, ...) values("HBSN67890", ...);';
 * const snH = findSN(logLineH);
 * // snH is "HBSN67890"
 * ```
 */
export const findSN = (text: string): string | null => {
  const snRegex = /insert into (?:g_support|h_support).*values\s*\("([^"]+)"/;
  const match = snRegex.exec(text);
  return match ? match[1] : null;
};