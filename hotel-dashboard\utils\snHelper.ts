/**
 * Parses a string of SNs into a unique array of strings.
 * Supports comma, semicolon, and space as delimiters.
 * 
 * @param input The raw string input from the user.
 * @returns A unique array of trimmed SNs.
 */
export const parseSnInput = (input: string): string[] => {
  if (!input || input.trim() === "") {
    return [];
  }

  // Replace commas and semicolons with spaces to unify delimiters
  const normalizedInput = input.replace(/[,;]/g, " ");

  // Split by one or more spaces, then trim and filter out empty strings
  const sns = normalizedInput
    .split(/\s+/)
    .map(sn => sn.trim())
    .filter(sn => sn.length > 0);

  // Return a unique set of SNs
  return Array.from(new Set(sns));
};
/**
 * Finds the first SN from an "insert into g_support" SQL statement in a given text.
 * @param text The text to search within.
 * @returns The found SN or null if not found.
 */
export const findSN = (text: string): string | null => {
  const snRegex = /insert into g_support.*values\s*\("([^"]+)"/;
  const match = snRegex.exec(text);
  return match ? match[1] : null;
};