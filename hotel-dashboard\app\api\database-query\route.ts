import { NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

/**
 * 处理对数据库的 POST 查询请求。
 * 此函数接收一个包含 SQL 查询语句的 JSON 对象，然后调用一个外部的 C# 可执行文件 (`WrapperApp.exe`)
 * 来安全地执行该查询。C# 应用负责连接数据库、执行查询，并将结果写入一个临时的 JSON 文件。
 * 此函数随后读取该 JSON 文件，将数据返回给前端，并在操作完成后清理临时文件。
 *
 * @param {Request} request - Next.js 的请求对象，其 body 应包含一个 JSON 对象，格式为 { "query": "SELECT * FROM ..." }。
 * @returns {Promise<NextResponse>} - 一个 Promise，解析为 Next.js 的响应对象。
 *   - 成功时：返回一个包含 { success: true, data: [...] } 的 JSON 响应，其中 `data` 是查询结果。
 *   - 失败时：返回一个包含错误信息的 JSON 响应，并附带适当的 HTTP 状态码（400、500）。
 */
export async function POST(request: Request) {
  // filePath 用于存储 C# 应用生成的临时结果文件的路径，以便在 try...catch...finally 中都能访问。
  let filePath: string | null = null;

  try {
    // 1. 接收和验证输入
    const body = await request.json();
    const sqlQuery = body.query;

    if (!sqlQuery || typeof sqlQuery !== 'string') {
      return NextResponse.json({ error: 'Query is required and must be a string.' }, { status: 400 });
    }

    // 2. 准备执行外部 C# 应用
    // 解析 C# 可执行文件的路径。该应用被设计为处理数据库连接和查询的包装器。
    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    
    // 定义一个固定的结果文件路径，而不是从子进程的 stdout 中解析。
    // 这使得与 C# 应用的通信更加健壮。
    const tempDir = process.env.TEMP || process.env.TMP || 'C:\\Windows\\Temp';
    filePath = path.join(tempDir, 'db_query_result.json');
    
    // 在执行查询前，如果旧的结果文件存在，则先删除它，确保不会返回陈旧的数据。
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
    
    // 3. 安全地执行查询
    // 为了将 SQL 查询作为命令行参数传递，需要对双引号进行转义。
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    
    // 使用 PowerShell 执行命令，并将 stderr 重定向到 null (`2>$null`)。
    // 这样做是为了抑制 C# 应用可能产生的、对前端无用的控制台输出（例如调试信息），
    // 同时仍然能够捕获真正的错误。
    const powershellCommand = `& "${wrapperAppPath}" "${escapedQuery}" --silent 2>$null`;

    console.log(`Executing command: ${powershellCommand} in ${wrapperAppDir}`);

    // 使用 Promise 包装 `spawn` 调用，以便异步地等待子进程完成。
    const { stdout, stderr } = await new Promise<{stdout: string, stderr: string}>((resolve, reject) => {
      const child = spawn('powershell', ['-Command', powershellCommand], {
        cwd: wrapperAppDir, // 在 C# 应用的目录下执行，以确保它能找到所有依赖项。
        stdio: ['pipe', 'pipe', 'pipe'] // 捕获 stdout 和 stderr。
      });

      let stdoutData = '';
      let stderrData = '';

      child.stdout.on('data', (data) => {
        stdoutData += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderrData += data.toString();
      });

      child.on('close', (code) => {
        // 如果进程以非零代码退出，表示发生了错误。
        if (code !== 0) {
          console.log(`Process stderr: ${stderrData}`);
          console.log(`Process stdout: ${stdoutData}`);
          reject(new Error(`Process exited with code ${code}. Stderr: ${stderrData}. Stdout: ${stdoutData}`));
        } else {
          resolve({ stdout: stdoutData, stderr: stderrData });
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });

    console.log(`Process completed. Checking for file: ${filePath}`);

    // 4. 处理查询结果
    // 检查预期的结果文件是否已由 C# 应用成功创建。
    if (!fs.existsSync(filePath)) {
      console.error(`File not found: ${filePath}`);
      // 如果文件未找到，返回一个详细的错误信息，包括 stdout 和 stderr 的片段，以帮助调试。
      return NextResponse.json({
        error: 'Unable to find result file from C# wrapper.',
        details: `Expected file: ${filePath}`,
        stdout: stdout.substring(0, 500), // 截取前500个字符用于调试
        stderr: stderr.substring(0, 500)
      }, { status: 500 });
    }

    // 读取并解析 JSON 结果文件。
    const jsonContent = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(jsonContent);

    console.log(`Successfully read data from: ${filePath}`);

    // 5. 返回成功响应
    // 将查询结果成功返回给前端。
    return NextResponse.json({
      success: true,
      data,
      filePath: filePath
    });

  } catch (error) {
    // 6. 统一错误处理
    console.error('API Error:', error);
    
    // 在 `finally` 块中执行清理操作，确保即使发生错误，临时文件也能被删除。
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError);
      }
    }

    // 向前端返回一个通用的 500 错误响应。
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'An unknown error occurred.'
    }, { status: 500 });
  }
}
