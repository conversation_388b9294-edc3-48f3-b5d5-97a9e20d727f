"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx":
/*!***********************************************!*\
  !*** ./app/(dashboard)/log-analysis/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LogAnalysisPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/log-analysis/LogDisplayArea */ \"(app-pages-browser)/./components/log-analysis/LogDisplayArea.tsx\");\n/* harmony import */ var _components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/log-analysis/LogChartView */ \"(app-pages-browser)/./components/log-analysis/LogChartView.tsx\");\n/* harmony import */ var _components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/log-analysis/LogFileUpload */ \"(app-pages-browser)/./components/log-analysis/LogFileUpload.tsx\");\n/* harmony import */ var _components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/log-analysis/SnSearchBar */ \"(app-pages-browser)/./components/log-analysis/SnSearchBar.tsx\");\n/* harmony import */ var _components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/log-analysis/ImageNameSearch */ \"(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx\");\n/* harmony import */ var _components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/log-analysis/BatchExportCSV */ \"(app-pages-browser)/./components/log-analysis/BatchExportCSV.tsx\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/utils/snHelper */ \"(app-pages-browser)/./utils/snHelper.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/exportUtils */ \"(app-pages-browser)/./lib/exportUtils.ts\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./components/ui/progress.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction LogAnalysisPage() {\n    _s();\n    const [processedData, setProcessedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dataChunks, setDataChunks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Unified selection state\n    const [selectedBlockIds, setSelectedBlockIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // SN Search states\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSnSearching, setIsSnSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Image Name Search states\n    const [isImageNameSearching, setIsImageNameSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // --- Queued export states ---\n    const [exportQueue, setExportQueue] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [exportProgress, setExportProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const exportTargetContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const logParserWorker = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast)();\n    // --- Worker Initialization and Message Handling ---\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            logParserWorker.current = new Worker(__webpack_require__.tu(new URL(/* worker import */ __webpack_require__.p + __webpack_require__.u(\"_app-pages-browser_workers_logParser_worker_ts\"), __webpack_require__.b)));\n            logParserWorker.current.onmessage = ({\n                \"LogAnalysisPage.useEffect\": (event)=>{\n                    console.log('[Main Thread] Received message from worker:', event.data);\n                    // Destructure all potential properties from the message\n                    const { type, payload, error, allBlocks, matchedBlockIds } = event.data;\n                    switch(type){\n                        case 'PARSE_LOG_RESULT':\n                            setIsLoading(false);\n                            if (error) {\n                                handleError(error);\n                            } else {\n                                // Use allBlocks directly, as per previous bug fixes.\n                                handleDataProcessed(allBlocks || []);\n                            }\n                            break;\n                        case 'MATCH_BY_TIMESTAMP_RESULT':\n                            setIsImageNameSearching(false);\n                            if (error) {\n                                toast({\n                                    title: \"图片名称搜索失败\",\n                                    description: error,\n                                    variant: \"destructive\"\n                                });\n                            } else {\n                                // Ensure matchedBlockIds is handled correctly, even if payload is not the primary source\n                                const ids = payload && payload.matchedBlockIds || matchedBlockIds || [];\n                                setSelectedBlockIds({\n                                    \"LogAnalysisPage.useEffect\": (prevIds)=>new Set([\n                                            ...Array.from(prevIds),\n                                            ...ids\n                                        ])\n                                }[\"LogAnalysisPage.useEffect\"]);\n                                toast({\n                                    title: \"图片名称搜索完成\",\n                                    description: \"匹配到 \".concat(ids.length, \" 个新的数据块。\")\n                                });\n                            }\n                            break;\n                        default:\n                            break;\n                    }\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n            return ({\n                \"LogAnalysisPage.useEffect\": ()=>{\n                    var _logParserWorker_current;\n                    (_logParserWorker_current = logParserWorker.current) === null || _logParserWorker_current === void 0 ? void 0 : _logParserWorker_current.terminate();\n                }\n            })[\"LogAnalysisPage.useEffect\"];\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        toast\n    ]);\n    const handleFileProcessed = (logContent)=>{\n        if (!logParserWorker.current) {\n            handleError(\"日志解析器未初始化。\");\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSelectedBlockIds(new Set());\n        setProcessedData([]); // Reset processed data\n        setDataChunks([]); // Also reset legacy dataChunks if needed\n        toast({\n            title: \"开始处理\",\n            description: \"正在分析日志文件...\"\n        });\n        const message = {\n            type: 'PARSE_LOG',\n            payload: {\n                logContent\n            }\n        };\n        logParserWorker.current.postMessage(message);\n    };\n    const handleDataProcessed = (workerData)=>{\n        const processedData = workerData.map((block)=>({\n                ...block,\n                data: []\n            }));\n        setProcessedData(processedData);\n        setDataChunks(processedData); // Keep dataChunks in sync for now\n        toast({\n            title: \"处理完成\",\n            description: \"日志文件已成功解析。\"\n        });\n    };\n    const handleError = (errorMessage)=>{\n        setError(errorMessage);\n        setIsLoading(false);\n        toast({\n            title: \"处理错误\",\n            description: errorMessage,\n            variant: \"destructive\"\n        });\n    };\n    const handleBlockSelectionChanged = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\": (selectedIds)=>{\n            setSelectedBlockIds(selectedIds);\n        }\n    }[\"LogAnalysisPage.useCallback[handleBlockSelectionChanged]\"], []);\n    const selectedBlocks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[selectedBlocks]\": ()=>{\n            return processedData.filter({\n                \"LogAnalysisPage.useMemo[selectedBlocks]\": (block)=>selectedBlockIds.has(block.block_id)\n            }[\"LogAnalysisPage.useMemo[selectedBlocks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[selectedBlocks]\"], [\n        processedData,\n        selectedBlockIds\n    ]);\n    const chartDataForView = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[chartDataForView]\": ()=>{\n            return selectedBlocks;\n        }\n    }[\"LogAnalysisPage.useMemo[chartDataForView]\"], [\n        selectedBlocks\n    ]);\n    const handleSnSearch = (query)=>{\n        setSearchQuery(query);\n        setIsSnSearching(true);\n        const snsToSearch = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_8__.parseSnInput)(query).map((sn)=>sn.toUpperCase());\n        if (snsToSearch.length === 0) {\n            setSelectedBlockIds(new Set());\n            setIsSnSearching(false);\n            return;\n        }\n        const results = new Set();\n        processedData.forEach((block)=>{\n            const snFromId = block.block_id.split('_')[0].toUpperCase();\n            for (const sn of snsToSearch){\n                const isSnInSnsArray = Array.isArray(block.sns) && block.sns.some((blockSn)=>blockSn.toUpperCase() === sn);\n                if (snFromId === sn || isSnInSnsArray) {\n                    results.add(block.block_id);\n                    break;\n                }\n            }\n        });\n        setSelectedBlockIds(results);\n        toast({\n            title: \"SN搜索完成\",\n            description: \"找到 \".concat(results.size, \" 个相关数据块。\")\n        });\n    };\n    const handleClearSearch = ()=>{\n        setSearchQuery('');\n        setSelectedBlockIds(new Set());\n        setIsSnSearching(false);\n    };\n    const generateExportFilename = (block)=>{\n        if (!block) return \"unknown_block.png\";\n        // 1. Format Timestamp\n        let timestampPart = 'NODATE';\n        if (block.start_time) {\n            try {\n                const date = new Date(block.start_time.replace(',', '.'));\n                const y = date.getFullYear();\n                const m = (date.getMonth() + 1).toString().padStart(2, '0');\n                const d = date.getDate().toString().padStart(2, '0');\n                const h = date.getHours().toString().padStart(2, '0');\n                const min = date.getMinutes().toString().padStart(2, '0');\n                const s = date.getSeconds().toString().padStart(2, '0');\n                timestampPart = \"\".concat(y).concat(m).concat(d, \"_\").concat(h, \"_\").concat(min, \"_\").concat(s);\n            } catch (e) {\n            // Keep 'NODATE' on parsing error\n            }\n        }\n        // 2. Find SN\n        let snPart = 'NOSN';\n        if (Array.isArray(block.sns) && block.sns.length > 0 && block.sns[0]) {\n            snPart = block.sns[0];\n        } else {\n            const snFromId = block.block_id.split('_')[0];\n            if (snFromId && snFromId.toLowerCase() !== 'block') {\n                snPart = snFromId;\n            }\n        }\n        return \"\".concat(timestampPart, \"GBSN\").concat(snPart, \".png\");\n    };\n    const handleImageNameSearch = (timestamps)=>{\n        if (!logParserWorker.current) {\n            toast({\n                title: \"错误\",\n                description: \"日志解析器未初始化。\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        if (timestamps.length === 0) {\n            toast({\n                title: \"提示\",\n                description: \"没有从文件名中解析出有效的时间戳。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setIsImageNameSearching(true);\n        toast({\n            title: \"正在搜索...\",\n            description: \"根据 \".concat(timestamps.length, \" 个时间戳进行匹配。\")\n        });\n        const message = {\n            type: 'MATCH_BY_TIMESTAMP',\n            payload: {\n                timestamps\n            }\n        };\n        logParserWorker.current.postMessage(message);\n    };\n    const displayedDataChunks = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[displayedDataChunks]\": ()=>{\n            if (!isSnSearching) {\n                return processedData;\n            }\n            return processedData.filter({\n                \"LogAnalysisPage.useMemo[displayedDataChunks]\": (chunk)=>selectedBlockIds.has(chunk.block_id)\n            }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"]);\n        }\n    }[\"LogAnalysisPage.useMemo[displayedDataChunks]\"], [\n        isSnSearching,\n        processedData,\n        selectedBlockIds\n    ]);\n    const initiateExportProcess = (exportIds)=>{\n        if (exportIds.length === 0) {\n            toast({\n                title: \"没有内容可导出\",\n                description: \"请选择至少一个数据块进行导出。\",\n                variant: \"default\"\n            });\n            return;\n        }\n        setExportProgress({\n            completed: 0,\n            total: exportIds.length\n        });\n        setGeneratedImages([]);\n        setExportQueue([\n            ...exportIds\n        ]);\n        setCurrentlyExportingBlockId(exportIds[0]);\n        toast({\n            title: \"导出已开始\",\n            description: \"准备导出 \".concat(exportIds.length, \" 个图表...\")\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (!currentlyExportingBlockId) return;\n            const processBlock = {\n                \"LogAnalysisPage.useEffect.processBlock\": async ()=>{\n                    await new Promise({\n                        \"LogAnalysisPage.useEffect.processBlock\": (resolve)=>setTimeout(resolve, 100)\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                    const container = exportTargetContainerRef.current;\n                    if (!container) {\n                        console.error(\"Export container not found.\");\n                        return;\n                    }\n                    const blockToExport = processedData.find({\n                        \"LogAnalysisPage.useEffect.processBlock.blockToExport\": (b)=>b.block_id === currentlyExportingBlockId\n                    }[\"LogAnalysisPage.useEffect.processBlock.blockToExport\"]);\n                    if (!blockToExport) {\n                        console.error(\"Block with ID \".concat(currentlyExportingBlockId, \" not found in dataChunks.\"));\n                        // Move to the next item in the queue even if the block is not found\n                        setExportQueue({\n                            \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                                const newQueue = prevQueue.slice(1);\n                                setCurrentlyExportingBlockId(newQueue[0] || null);\n                                return newQueue;\n                            }\n                        }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        return;\n                    }\n                    const chartElement = container.querySelector('[data-block-id=\"'.concat(currentlyExportingBlockId, '\"]'));\n                    if (chartElement) {\n                        try {\n                            await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.waitForChartReady)(chartElement);\n                            const blob = await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.generateSingleImageBlob)(chartElement);\n                            const filename = generateExportFilename(blockToExport);\n                            setGeneratedImages({\n                                \"LogAnalysisPage.useEffect.processBlock\": (prev)=>[\n                                        ...prev,\n                                        {\n                                            filename,\n                                            blob\n                                        }\n                                    ]\n                            }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                        } catch (error) {\n                            toast({\n                                title: \"图表生成失败\",\n                                description: \"无法为数据块 \".concat(currentlyExportingBlockId, \" 生成图片。\"),\n                                variant: \"destructive\"\n                            });\n                        }\n                    } else {\n                        console.warn(\"Chart element for block ID \".concat(currentlyExportingBlockId, \" not found in DOM.\"));\n                    }\n                    // Advance the queue\n                    setExportQueue({\n                        \"LogAnalysisPage.useEffect.processBlock\": (prevQueue)=>{\n                            const newQueue = prevQueue.slice(1);\n                            setCurrentlyExportingBlockId(newQueue[0] || null);\n                            return newQueue;\n                        }\n                    }[\"LogAnalysisPage.useEffect.processBlock\"]);\n                }\n            }[\"LogAnalysisPage.useEffect.processBlock\"];\n            processBlock();\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        processedData,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress) {\n                setExportProgress({\n                    \"LogAnalysisPage.useEffect\": (prev)=>({\n                            ...prev,\n                            completed: generatedImages.length\n                        })\n                }[\"LogAnalysisPage.useEffect\"]);\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        generatedImages\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LogAnalysisPage.useEffect\": ()=>{\n            if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {\n                if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {\n                    const zipAndDownload = {\n                        \"LogAnalysisPage.useEffect.zipAndDownload\": async ()=>{\n                            try {\n                                await (0,_lib_exportUtils__WEBPACK_IMPORTED_MODULE_11__.zipAndDownloadImages)(generatedImages, 'exported_log_charts');\n                                toast({\n                                    title: \"导出成功\",\n                                    description: \"已将 \".concat(generatedImages.length, \" 个图表导出为压缩包。\")\n                                });\n                            } catch (error) {\n                                toast({\n                                    title: \"导出失败\",\n                                    description: \"无法创建或下载ZIP文件。\",\n                                    variant: \"destructive\"\n                                });\n                            } finally{\n                                setExportQueue([]);\n                                setCurrentlyExportingBlockId(null);\n                                setGeneratedImages([]);\n                                setExportProgress(null);\n                            }\n                        }\n                    }[\"LogAnalysisPage.useEffect.zipAndDownload\"];\n                    zipAndDownload();\n                } else if (exportProgress.total > 0) {\n                    toast({\n                        title: \"导出完成\",\n                        description: \"成功导出 \".concat(generatedImages.length, \" 个图表，\").concat(exportProgress.total - generatedImages.length, \" 个失败。\"),\n                        variant: \"default\"\n                    });\n                    setExportQueue([]);\n                    setCurrentlyExportingBlockId(null);\n                    setGeneratedImages([]);\n                    setExportProgress(null);\n                }\n            }\n        }\n    }[\"LogAnalysisPage.useEffect\"], [\n        currentlyExportingBlockId,\n        exportProgress,\n        generatedImages,\n        exportQueue.length,\n        toast\n    ]);\n    const blockToRenderOffscreen = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": ()=>{\n            if (!currentlyExportingBlockId) return null;\n            return processedData.find({\n                \"LogAnalysisPage.useMemo[blockToRenderOffscreen]\": (b)=>b.block_id === currentlyExportingBlockId\n            }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"]) || null;\n        }\n    }[\"LogAnalysisPage.useMemo[blockToRenderOffscreen]\"], [\n        currentlyExportingBlockId,\n        processedData\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full p-4 gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"日志分析与查询\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 357,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-1 space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogFileUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                onFileProcessed: handleFileProcessed,\n                                isProcessing: isLoading,\n                                disabled: isLoading || !!exportProgress || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_SnSearchBar__WEBPACK_IMPORTED_MODULE_5__.SnSearchBar, {\n                                onSearch: handleSnSearch,\n                                onClear: handleClearSearch,\n                                isLoading: isLoading || isImageNameSearching\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 12\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_ImageNameSearch__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                onSearch: handleImageNameSearch,\n                                isLoading: isImageNameSearching,\n                                disabled: processedData.length === 0 || isLoading || !!exportProgress\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 371,\n                                columnNumber: 12\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogDisplayArea__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            dataChunks: displayedDataChunks,\n                            onSelectionChange: handleBlockSelectionChanged,\n                            onStartExport: initiateExportProcess,\n                            selectedBlockIds: selectedBlockIds,\n                            isSearching: isSnSearching\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 359,\n                columnNumber: 7\n            }, this),\n            exportProgress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.Alert, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.AlertTitle, {\n                        children: \"正在导出图表...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_12__.AlertDescription, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 mt-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_13__.Progress, {\n                                    value: exportProgress.completed / exportProgress.total * 100,\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"\".concat(exportProgress.completed, \" / \").concat(exportProgress.total)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 389,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_BatchExportCSV__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    selectedBlocks: selectedBlocks\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-4\",\n                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: \"正在处理文件...\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 407,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center bg-destructive/10\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-destructive font-semibold\",\n                                children: \"发生错误\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 414,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                                lineNumber: 415,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this) : isSnSearching && displayedDataChunks.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                '未找到与 \"',\n                                searchQuery,\n                                '\" 相关的日志块。'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 16\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 14\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 12\n                }, this) : chartDataForView.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    dataChunks: chartDataForView,\n                    selectedBlockIds: Array.from(selectedBlockIds),\n                    onBlockSelect: ()=>{},\n                    isHighlighted: isSnSearching\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                    className: \"h-full flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-muted-foreground\",\n                            children: processedData.length > 0 ? \"请从左侧选择数据块以显示图表\" : \"请先上传日志文件\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 434,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                ref: exportTargetContainerRef,\n                style: {\n                    position: 'absolute',\n                    left: '-9999px',\n                    top: '-9999px',\n                    width: '1200px',\n                    height: '800px'\n                },\n                children: blockToRenderOffscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_log_analysis_LogChartView__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    dataChunks: [\n                        blockToRenderOffscreen\n                    ],\n                    selectedBlockIds: [\n                        blockToRenderOffscreen.block_id\n                    ],\n                    onBlockSelect: ()=>{}\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                    lineNumber: 452,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n                lineNumber: 447,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\app\\\\(dashboard)\\\\log-analysis\\\\page.tsx\",\n        lineNumber: 356,\n        columnNumber: 5\n    }, this);\n}\n_s(LogAnalysisPage, \"Xoh4hCVUPPNdSGDjbAqOmkyjQgQ=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_10__.useToast\n    ];\n});\n_c = LogAnalysisPage;\nvar _c;\n$RefreshReg$(_c, \"LogAnalysisPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/(dashboard)/log-analysis/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx":
/*!*****************************************************!*\
  !*** ./components/log-analysis/ImageNameSearch.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst ImageNameSearch = (param)=>{\n    let { onSearch, disabled, isLoading } = param;\n    _s();\n    const [inputText, setInputText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleSearch = ()=>{\n        setError(null);\n        const fileNames = inputText.split(\"\\n\").filter((name)=>name.trim() !== \"\");\n        if (fileNames.length === 0) {\n            setError(\"Please enter at least one file name.\");\n            return;\n        }\n        const timestamps = [];\n        let hasError = false;\n        fileNames.forEach((fileName)=>{\n            // New regex to match YYYYMMDD_HH_mm_ss format\n            const match = fileName.match(/^(\\d{8})_(\\d{2})_(\\d{2})_(\\d{2})/);\n            if (match) {\n                const [, datePart, hourPart, minutePart, secondPart] = match;\n                const year = parseInt(datePart.substring(0, 4), 10);\n                const month = parseInt(datePart.substring(4, 6), 10) - 1; // Month is 0-indexed\n                const day = parseInt(datePart.substring(6, 8), 10);\n                const hour = parseInt(hourPart, 10);\n                const minute = parseInt(minutePart, 10);\n                const second = parseInt(secondPart, 10);\n                const date = new Date(year, month, day, hour, minute, second);\n                if (!isNaN(date.getTime())) {\n                    timestamps.push(Math.floor(date.getTime() / 1000));\n                } else {\n                    hasError = true;\n                }\n            } else {\n                hasError = true;\n            }\n        });\n        if (hasError) {\n            setError(\"Some file names have an incorrect format. Please use 'YYYYMMDD_HH_mm_ssGBSNxxxxxx.png' and ensure each is on a new line.\");\n        }\n        if (timestamps.length > 0) {\n            onSearch(timestamps);\n        } else if (!hasError) {\n            setError(\"No valid timestamps could be extracted. Please check the format.\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid w-full gap-1.5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_2__.Label, {\n                        htmlFor: \"image-names\",\n                        children: \"Image File Names\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_3__.Textarea, {\n                        id: \"image-names\",\n                        placeholder: \"Paste image file names here, one per line...\",\n                        value: inputText,\n                        onChange: (e)=>setInputText(e.target.value),\n                        rows: 5,\n                        disabled: disabled || isLoading\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-muted-foreground\",\n                        children: \"Example: `20230101_12_00_00GBSN123456.png`\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                variant: \"destructive\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                onClick: handleSearch,\n                disabled: disabled || isLoading,\n                children: isLoading ? \"Searching...\" : \"Search by Image Names\"\n            }, void 0, false, {\n                fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\pycode\\\\support_chart2\\\\hotel-dashboard\\\\components\\\\log-analysis\\\\ImageNameSearch.tsx\",\n        lineNumber: 66,\n        columnNumber: 5\n    }, undefined);\n};\n_s(ImageNameSearch, \"kh4Ca/V8KQhWjZV3rP68BzLKf/k=\");\n_c = ImageNameSearch;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageNameSearch);\nvar _c;\n$RefreshReg$(_c, \"ImageNameSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/log-analysis/ImageNameSearch.tsx\n"));

/***/ })

});