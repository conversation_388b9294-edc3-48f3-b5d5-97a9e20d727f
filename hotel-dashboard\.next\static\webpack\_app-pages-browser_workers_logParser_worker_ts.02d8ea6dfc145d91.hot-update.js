"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.module.ts":
/*!*************************************!*\
  !*** ./workers/logParser.module.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findBlocksByTimestamp: () => (/* binding */ findBlocksByTimestamp),\n/* harmony export */   processLogFile: () => (/* binding */ processLogFile)\n/* harmony export */ });\n/* harmony import */ var _dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataExtractor.module */ \"(app-pages-browser)/./workers/dataExtractor.module.ts\");\n\n/**\r\n * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。\r\n * @param logContent 完整的日志内容字符串。\r\n * @returns 处理后的日志结果。\r\n */ function processLogFile(logContent) {\n    const lines = logContent.split(/\\r?\\n/);\n    const results = [];\n    let v1Count = 0;\n    let v2Count = 0;\n    let unknownCount = 0;\n    let endOfBlock = -1;\n    for(let i = lines.length - 1; i >= 0; i--){\n        // V1 and V2 blocks end with 'insert into g_support'\n        if (lines[i].includes('insert into g_support')) {\n            endOfBlock = i;\n            let startOfBlock = -1;\n            for(let j = endOfBlock; j >= 0; j--){\n                // V1 and V2 blocks start with '开始抽真空'\n                if (lines[j].includes('开始抽真空')) {\n                    console.debug(j);\n                    startOfBlock = j;\n                    break;\n                }\n            }\n            if (startOfBlock !== -1) {\n                const blockLines = lines.slice(startOfBlock, endOfBlock + 1);\n                const processedBlock = (0,_dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__.extractDataFromBlock)(blockLines);\n                results.push(processedBlock);\n                // Simple version detection for statistics\n                // This can be improved if more specific rules are available\n                if (processedBlock.version === 'V1') {\n                    v1Count++;\n                } else if (processedBlock.version === 'V2') {\n                    v2Count++;\n                } else {\n                    unknownCount++;\n                }\n                // Move the index to the beginning of the found block to avoid re-processing\n                i = startOfBlock;\n            }\n        }\n    }\n    // Since we are iterating backwards, reverse the results to restore chronological order\n    results.reverse();\n    return {\n        blocks: results,\n        totalLines: lines.length,\n        v1Count,\n        v2Count,\n        unknownCount\n    };\n}\n/**\r\n * Finds block IDs that match a given list of timestamps.\r\n * @param blocks The array of processed log blocks.\r\n * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.\r\n * @returns An array of block_ids that match the timestamps.\r\n */ function findBlocksByTimestamp(blocks, timestampsToMatch) {\n    const timestampSet = new Set(timestampsToMatch);\n    const matchedBlockIds = [];\n    for (const block of blocks){\n        if (block.start_time) {\n            try {\n                // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'\n                const date = new Date(block.start_time.replace(',', '.'));\n                const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);\n                if (timestampSet.has(blockTimestampInSeconds)) {\n                    matchedBlockIds.push(block.block_id);\n                }\n            } catch (e) {\n                console.error(\"Error parsing date for block \".concat(block.block_id, \":\"), e);\n            }\n        }\n    }\n    return matchedBlockIds;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.module.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("3e91454e52983861")
/******/ })();
/******/ 
/******/ }
);