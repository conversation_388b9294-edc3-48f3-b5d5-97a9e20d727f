"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { X } from "lucide-react";

interface UnifiedLogSearchProps {
  onSearch: (searchTerms: string[]) => void;
  onClear: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

export function UnifiedLogSearch({
  onSearch,
  onClear,
  isLoading = false,
  disabled = false,
}: UnifiedLogSearchProps) {
  const [inputText, setInputText] = useState("");

  const handleSearch = () => {
    const searchTerms = inputText
      .split('\n')
      .map(term => term.trim())
      .filter(term => term !== '');
    
    if (searchTerms.length > 0) {
      onSearch(searchTerms);
    }
  };

  const handleClear = () => {
    setInputText("");
    onClear();
  };

  return (
    <div className="grid w-full gap-2">
      <Label htmlFor="unified-search">统一搜索</Label>
      <Textarea
        id="unified-search"
        placeholder="输入SN、时间戳或数据块名，每行一个..."
        value={inputText}
        onChange={(e) => setInputText(e.target.value)}
        disabled={isLoading || disabled}
        rows={5}
      />
      <div className="flex items-center space-x-2">
        <Button
          onClick={handleSearch}
          disabled={isLoading || disabled || inputText.trim() === ""}
          className="flex-grow"
        >
          搜索
        </Button>
        <Button
          variant="outline"
          size="icon"
          onClick={handleClear}
          disabled={disabled}
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}