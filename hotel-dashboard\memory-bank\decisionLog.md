# Decision Log

This file records architectural and implementation decisions using a list format.
2025-07-18 15:44:00 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision
[2025-07-18 15:45:52] - 设计并确认了“导出为 XLSX V2”功能的最终架构，以支持按 SN 汇总导出关键数据和图表。

**Rationale:**
根据用户更新的需求，原有的“一数据块一工作表”的导出模式已不适用。新的架构将多个数据块按 SN 聚合，提取特定的新数据点（`preVentCollimatorDiff`, `collimatorDiffAfterUV`），并将图表直接嵌入单张汇总工作表中，更符合用户的分析习惯。

**Implementation Details:**

#### 1. 功能概述
该功能允许用户根据搜索到的SN（序列号），将相关数据块的关键信息（包括一个聚合图表和两个特定的准直 `diff` 值）导出到一个单一的 `.xlsx` 文件中。每个SN在文件中对应一行记录。

#### 2. 技术依赖 (NPM Packages)
- **`exceljs`**: 用于在客户端构建结构复杂的 XLSX 文件，包括嵌入图表。
- **`file-saver`**: 用于在浏览器端触发文件下载。
- **`dom-to-image-more`**: 复用现有能力，将前端图表（离屏渲染）转换成图片以下载。

#### 3. 数据模型变更
在现有的数据块（Log Block）JSON 结构中，需要新增以下两个字段：

```typescript
interface LogBlock {
  // ... existing fields
  preVentCollimatorDiff: number | null;
  collimatorDiffAfterUV: number | null;
}
```

- `preVentCollimatorDiff`: 打开放气阀事件前的第一个准直 `diff` 值。
- `collimatorDiffAfterUV`: `INSERT INTO g_support` 语句中的 `ColimatorDiff_afterUV` 值。

#### 4. 核心流程设计
1.  **数据解析 (Worker Thread)**:
    -   在 `workers/logParser.worker.ts` 中，增强解析逻辑。
    -   增加规则（可能在 `workers/logParser.definitions.ts`）来捕获“打开放气阀”事件和 `INSERT INTO g_support` 语句。
    -   在构建数据块 JSON 时，解析并填充 `preVentCollimatorDiff` 和 `collimatorDiffAfterUV` 字段。

2.  **用户交互 (Frontend)**:
    -   用户在 `UnifiedLogSearch` 中输入 SN 并搜索。
    -   `app/(dashboard)/log-analysis/page.tsx` 的 `handleSearch` 触发搜索，并更新 `selectedBlockIds` 状态。
    -   `LogDisplayArea` 组件中的“导出为XLSX”按钮被激活。

3.  **导出流程触发 (Frontend)**:
    -   用户点击“导出为XLSX”按钮，调用 `page.tsx` 中的 `handleExportXlsx` 函数。

4.  **数据聚合与文件生成 (Utility Module)**:
    -   `handleExportXlsx` 调用 `lib/exportUtils.ts` 中的新核心函数 `exportSnDataToXlsx(selectedBlocks: LogBlock[])`。
    -   `exportSnDataToXlsx` 执行以下操作：
        a.  **按 SN 分组**: 将传入的 `selectedBlocks` 数组按 `SN` 字段进行分组，得到一个类似 `Map&lt;string, LogBlock[]&gt;` 的结构。
        b.  **并行处理**: 对每个 SN 分组，并行执行以下任务：
            i.  **图表生成**:
                -   找到该 SN 对应的 `LogChartView` 组件实例（或动态创建一个离屏实例）。
                -   使用 `dom-to-image-more` 将图表渲染为 PNG 图片的 Base64 字符串。
            ii. **数据提取**:
                -   从该 SN 的数据块中提取 `preVentCollimatorDiff` 和 `collimatorDiffAfterUV`。**决策：** 如果一个 SN 对应多个数据块，优先选择包含非 `null` 值的数据。如果存在多个非 `null` 值，则选择第一个数据块的值。
        c.  **构建 XLSX**:
            -   使用 `exceljs` 创建一个新的 Workbook。
            -   创建一个名为 `SN Log Export` 的工作表。
            -   添加表头：`["SN", "Chart", "Pre-Vent Diff", "After-UV Diff"]`。
            -   遍历处理完的每个 SN 分组数据，将 SN、提取的两个 `diff` 值填入单元格。
            -   将生成的图表图片添加到对应的 "Chart" 单元格中，并调整行高和列宽以适应图表。
        d.  **触发下载**:
            -   将 `exceljs` 生成的 `ArrayBuffer` 传递给 `file-saver`。
            -   生成文件名 `sn_export_[YYYYMMDD_HHMMSS].xlsx` 并触发下载。

#### 5. XLSX 文件结构
- **文件名**: `sn_export_[YYYYMMDD_HHMMSS].xlsx`
- **工作表**: `SN Log Export`
- **列定义**:
| 列 | 标题 | 内容 | 备注 |
|---|---|---|---|
| A | SN | 设备的序列号 | |
| B | Chart | 对应SN的折线图 | 图片格式，嵌入单元格 |
| C | Pre-Vent Diff | `preVentCollimatorDiff` 的值 | |
| D | After-UV Diff | `collimatorDiffAfterUV` 的值 | |

#### 6. 模块职责划分
- **`workers/logParser.worker.ts`**: **修改**。实现新的数据点提取逻辑。
- **`lib/exportUtils.ts`**: **修改/新增**。创建 `exportSnDataToXlsx` 函数，实现数据聚合、图表处理和XLSX文件生成。
- **`app/(dashboard)/log-analysis/page.tsx`**: **修改**。添加 `handleExportXlsx` 函数，获取完整的 `LogBlock` 对象并传递给导出工具。
- **`types/*.ts` (或相关类型定义文件)**: **修改**。更新 `LogBlock` 接口。

---
### Decision (Debug)
[2025-07-18 16:28:00] - [Bug Fix Strategy: Implement Robust Polling for Chart Rendering]

**Rationale:**
The previous fix for the XLSX export feature was ineffective. The root cause remains a race condition where `domtoimage.toPng` is called before the Recharts library has fully rendered the chart in the offscreen div. The `setTimeout` and initial `waitForChartReady` implementation were not sufficient to guarantee rendering completion. The new solution replaces this fragile waiting mechanism with a robust, Promise-based polling function (`pollForElement`) that actively checks for the presence of a deep SVG element (e.g., `.recharts-surface`) within the chart container. This ensures that the screenshot is only taken after the chart has verifiably completed its rendering process, thus preventing blank or incomplete chart images in the exported XLSX file.

**Details:**
- **Affected File:** `app/(dashboard)/log-analysis/page.tsx`
- **Key Function:** `handleExportXlsx`
- **New Implementation:**
    1. A new `pollForElement` utility function was added. It repeatedly checks for a specified DOM element with a configurable timeout.
    2. The `handleExportXlsx` function was refactored to use `await pollForElement(container, 'svg .recharts-surface', 5000);` for each chart before calling `generateSingleImageBlob`.
    3. The unreliable `setTimeout` and the less specific `waitForChartReady` calls were removed from the XLSX export logic.

---
### Decision (Debug)
[2025-07-18 16:35:00] - [Bug Fix Strategy: Robust Regex and Code Logic for SQL INSERT Parsing]

**Rationale:**
The previous regular expression for extracting `collimatorDiffAfterUV` was too simplistic and failed when the column order in the `INSERT` statement changed or if the value was not quoted. The fix involves a two-part strategy:
1.  A more robust regular expression in `workers/rulesEngine.ts` that captures the entire list of column names and the entire list of values, rather than trying to find a specific value directly.
2.  Updated application logic in `workers/dataExtractor.module.ts` to process the results of the new regex. This code now programmatically finds the index of the `ColimatorDiff_afterUV` column and uses that index to retrieve the correct value from the list of values. This approach is resilient to changes in column order and formatting variations in the SQL statement.

**Details:**
- **Affected Files:**
  - `workers/rulesEngine.ts`
  - `workers/dataExtractor.module.ts`
- **Key Changes:**
    - **`REGEX_INSERT_AFTER_UV_DIFF` in `rulesEngine.ts`:** Changed from a simple value search to `/insert into \w+\s*\(([^)]+)\)\s*values\s*\(([^)]+)\)/`, which captures column and value lists.
    - **`extractDataFromBlock` in `dataExtractor.module.ts`:** The logic for handling the `INSERT` statement was rewritten to split the captured column and value strings into arrays, find the index of the target column, and extract the corresponding value, cleaning it of any quotes.

---
### Decision (Code)
[2025-07-21 09:16:36] - Refactored XLSX export to fix asynchronous race condition.

**Rationale:**
The original `handleExportXlsx` function used a synchronous `for...in` loop to iterate over SN groups and trigger offscreen chart rendering via React's `setState`. This created a race condition where the loop would finish before React could process the state updates and render the charts, resulting in only the last chart being captured for all entries.

The fix involves refactoring the process into a fully asynchronous workflow. A new helper function, `generateChartForSn`, was created to encapsulate the logic for rendering a single chart. This function uses `async/await` and `setTimeout` delays to ensure React has time to render the component offscreen before the image capture is attempted. The main `handleExportXlsx` function now uses a `for...of` loop to `await` the completion of `generateChartForSn` for each SN, ensuring a sequential and predictable execution order.

**Details:**
- **File Modified:** `app/(dashboard)/log-analysis/page.tsx`
- **Functions Changed:** `handleExportXlsx`
- **Functions Added:** `generateChartForSn`

---
### Decision (Debug)
[2025-07-21 09:20:36] - [Bug Fix Strategy: Replace setTimeout with Callback/Promise for Chart Export]

**Rationale:**
The original XLSX export function failed to export multiple charts because it relied on a fixed `setTimeout` to wait for React's asynchronous rendering. This was unreliable, causing the export loop to proceed before the chart component was actually rendered, resulting in only the last item being captured. The fix involves creating a robust synchronization mechanism. The child chart component (`LogChartView`) is modified to accept an `onRenderComplete` callback. The parent page (`LogAnalysisPage`) now passes a `Promise` resolver as this callback. This ensures the export loop explicitly `await`s a signal directly from the component, confirming it has rendered, before proceeding to capture the image. This callback-based approach is deterministic and guarantees each chart is rendered and captured sequentially.

**Details:**
*   **Affected Files:**
    *   `app/(dashboard)/log-analysis/page.tsx`
    *   `components/log-analysis/LogChartView.tsx`
*   **Key Change:** Removed `pollForElement` and `setTimeout` waits in `generateChartForSn`. Implemented a `Promise`-based flow controlled by an `onRenderComplete` callback passed to the offscreen `LogChartView` instance.

---
### Decision (Debug)
[2025-07-21 01:29:52] - [Bug Fix Strategy: Isolate Iteration Failures in Data Export Loop]

**Rationale:**
The XLSX export feature was failing to export all selected items, processing only the first one before halting. The root cause was an unhandled exception within the `for...of` loop in the `handleExportXlsx` function. Specifically, an error during the asynchronous `generateChartForSn` function call would crash the entire loop, preventing subsequent items from being processed. The fix isolates failures to a single iteration.

**Details:**
- **Affected File:** `app/(dashboard)/log-analysis/page.tsx`
- **Key Change:** A `try...catch` block was wrapped around the `await generateChartForSn(...)` call inside the main `for...of` loop.
- **Impact:**
    - If chart generation for a single SN fails, the error is now caught and logged to the console.
    - Crucially, the loop continues to the next iteration, ensuring all other SNs are processed.
    - The `allSnDataForExport.push()` call was positioned outside the `try...catch` block to guarantee that the basic textual data for every SN is added to the export list, regardless of whether its corresponding chart was successfully generated. This ensures data completeness for all entries.

---
### Decision (Code)
[2025-07-21 01:41:30] - Refactored XLSX export feature to align with new specifications.

**Rationale:**
The previous implementation incorrectly grouped data by SN. The new business requirement is to export a separate row for each user-selected data block. This change simplifies the logic by removing the need for data aggregation and complex asynchronous handling of grouped data. The new implementation processes each selected block independently, generates a chart, and creates a corresponding row in the XLSX file.

**Details:**
- **File Modified:** [`app/(dashboard)/log-analysis/page.tsx`](app/(dashboard)/log-analysis/page.tsx)
  - `handleExportXlsx` was completely rewritten. It now iterates over `selectedBlocks` using `Promise.all` for parallel processing.
  - The `generateChartForSn` helper was replaced with `generateChartForBlock`, which now returns a base64 string instead of an ArrayBuffer.
- **File Modified:** [`lib/exportUtils.ts`](lib/exportUtils.ts)
  - The `exportSnDataToXlsx` function was replaced.
  - Switched from `exceljs` to the `xlsx` library to better handle base64 image embedding.
  - The function now accepts an array of row objects and constructs the worksheet according to the new, non-aggregated format.
- **Dependencies:**
  - Uninstalled `exceljs`.
  - Installed `xlsx` and `@types/xlsx`.

---
### Decision (Debug)
[2025-07-21 01:45:31] - [Bug Fix Strategy: Add Comprehensive Error Handling to XLSX Export]

**Rationale:**
The XLSX export feature was failing silently after a recent refactoring that introduced a `Promise.all` chain. The root cause was identified as an unhandled promise rejection. When any part of the asynchronous chart generation process failed, the entire `Promise.all` would reject without a `.catch()` block, causing the function to terminate prematurely without any error message.

**Details:**
- **Affected Files:**
  - `app/(dashboard)/log-analysis/page.tsx`
  - `lib/exportUtils.ts`
- **Key Changes:**
    1.  **Added Global Catch:** A `.catch()` block was added to the main `Promise.all(...)` chain in the `handleExportXlsx` function. This ensures that any unexpected failure in the promise chain is caught, logged to the console, and reported to the user via a toast notification.
    2.  **Isolated Block Failures:** A `try...catch` block was wrapped around the `generateChartForBlock` call within the `.map()` iterator. If a single block fails to process (e.g., chart generation error), the error is logged, and a placeholder object (`{ Chart: "GENERATION FAILED" }`) is returned. This prevents one failed block from terminating the entire export process for all other selected blocks.
    3.  **Diagnostic Logging:** Added `console.log` statements to the beginning and end of the `exportSnDataToXlsx` function in `lib/exportUtils.ts` to confirm it's being called correctly and to trace the data flow.

---
### Decision (Debug)
[2025-07-21 01:48:48] - [Bug Fix Strategy: Sequential Execution for Chart Export]

**Rationale:**
The XLSX export feature was critically failing due to a race condition in the `handleExportXlsx` function. The use of `Promise.all` to process chart generation in parallel caused multiple asynchronous tasks to compete for the same off-screen rendering container and React state. This resulted in only one chart being successfully generated while the others would hang indefinitely, stalling the entire export process. The definitive fix was to enforce sequential execution.

**Details:**
- **Affected File:** [`app/(dashboard)/log-analysis/page.tsx`](app/(dashboard)/log-analysis/page.tsx)
- **Key Change:** The parallel `selectedBlocks.map(...)` and `Promise.all(...)` structure within `handleExportXlsx` was completely replaced with a serial `for...of` loop.
- **Impact:**
    - Each selected data block now completes a full "render -> screenshot -> cleanup" cycle in sequence.
    - The `await generateChartForBlock(block)` call inside the loop ensures that the process waits for one chart to be fully generated and its data captured before starting the next.
    - This change eliminates the race condition, ensuring that all selected charts are reliably generated and included in the final XLSX export.

---
### Decision (Code)
[2025-07-21 01:56:51] - Replaced `xlsx` with `exceljs` for XLSX export to enable image embedding.

**Rationale:**
The previously used `xlsx`

---
### Decision (Code)
[2025-07-21 10:07:28] - Implement dynamic timestamp in XLSX export filename.

**Rationale:**
The user requested that each exported XLSX file has a unique name to prevent overwriting and to provide a clear record of when the export was generated. Adding a `YYYYMMDD_HHMMSS` timestamp to the filename is a standard and effective way to achieve this.

**Details:**
Modified `handleExportXlsx` in [`app/(dashboard)/log-analysis/page.tsx`](app/(dashboard)/log-analysis/page.tsx) to generate a timestamp and append it to the base filename before calling the `exportSnDataToXlsx` utility function.

---
### Decision (Code)
[2025-07-21 11:09:17] - 在实现 OQC 查询 API (`/api/oqc-query`) 时，复用现有的通过外部 C# 应用 (`WrapperApp.exe`) 执行数据库查询的系统模式。

**Rationale:**
为了保持与项目现有架构的一致性并避免引入新的数据库连接库，新的 API 采用了与 `/api/database-query` 相同的机制。此方法将数据库访问逻辑集中在 C# 应用中，简化了 Node.js 后端的职责，并统一了数据访问模式。

**Details:**
*   **File:** [`app/api/oqc-query/route.ts`](app/api/oqc-query/route.ts:1)
*   **Implementation:** Node.js 后端通过 `spawn` 调用 `WrapperApp.exe`，传递 SQL 查询语句，并从临时 JSON 文件中读取结果。

---
### Decision (Code)
[2025-07-21 11:48:53] - **修正**: 将OQC查询的 `sourceTable` 提取逻辑从前端组件移动到后端的 `logParser.worker`。

**Rationale:**
调试发现，前端组件中的 `block.raw_content` 并不包含原始的 `INSERT INTO` 语句，因为该语句在日志解析阶段已被处理。因此，在前端进行字符串匹配是不可靠的。正确的做法是在日志解析器 (`logParser.module.ts`) 识别到 `insert into` 语句时，就立刻确定来源表，并将其作为一个新字段 (`sourceTable`) 添加到 `ProcessedBlock` 对象中。这样，前端可以直接、可靠地使用此字段，无需进行任何字符串解析。

**Details:**
*   **File Modified:** [`workers/logParser.module.ts`](workers/logParser.module.ts:1), [`workers/dataExtractor.module.ts`](workers/dataExtractor.module.ts:1), [`app/(dashboard)/log-analysis/page.tsx`](app/(dashboard)/log-analysis/page.tsx:1)
*   **Interface Change:** 在 [`workers/logParser.definitions.ts`](workers/logParser.definitions.ts:1) 的 `ProcessedBlock` 接口中添加了 `sourceTable` 字段。

---
### Decision (Code)
[2025-07-21 13:04:50] - **重大逻辑简化**: 根据用户最终反馈，决定废弃通过解析日志内容来判断OQC查询来源表的逻辑。新的、更直接的逻辑是根据SN的首字母来判断。

**Rationale:**
之前的实现试图通过解析日志内容来寻找`INSERT INTO`语句，但这被证明是复杂且不可靠的。用户提出的新规则——SN以'G'开头查`g_oqc_zhunzhi`表，以'H'开头查`h_oqc_zhunzhi`表——极大地简化了实现。此方法将所有判断逻辑都放在了前端API调用之前，无需修改后端日志解析器，使代码更简洁、更健壮，且完全符合业务需求。

**Details:**
*   **File Modified:** [`app/(dashboard)/log-analysis/page.tsx`](app/(dashboard)/log-analysis/page.tsx:1)
*   **File Reverted:** `workers/logParser.definitions.ts`, `workers/logParser.module.ts`, `workers/dataExtractor.module.ts` 已恢复到修改前状态。
*   **Implementation:** 在 `handleExportXlsx` 函数中，直接检查 `block.sns[0]` 的首字母来确定 `sourceTable`。

---
### Decision (Code)
[2025-07-21 13:26:38] - **最终修复**: 修正了后端API (`/api/oqc-query`) 中引用的临时结果文件名。

**Rationale:**
经过多轮调试，最终发现问题根源在于Node.js后端代码试图读取一个错误的文件名 (`oqc_query_result.json`)。用户确认，C#包装器应用实际写入的文件是 `db_query_result.json`。通过将代码中引用的文件名更改为与C#应用实际输出的文件名一致，数据流被打通，问题得到解决。同时，所有为调试而添加的日志代码均已移除。

**Details:**
*   **File Modified:** [`app/api/oqc-query/route.ts`](app/api/oqc-query/route.ts:1)
*   **Change:** 将 `filePath` 的目标文件名从 `oqc_query_result.json` 修改为 `db_query_result.json`。

---
### Decision (Code)
[2025-07-21 13:36:18] - **最终修复**: 修正了后端API (`/api/oqc-query`) 中对C#程序返回的JSON的解析逻辑。

**Rationale:**
经过最终调试，确认了C#程序返回的JSON结构为 `{ "Table1": [...] }`，而不是一个直接的数组。之前的代码因此无法正确提取`diff`值。通过修改解析逻辑，先从返回的对象中提取`Table1`数组，再从中获取`diff`值，问题得到彻底解决。同时，所有调试代码均已移除，代码库已清理干净。

**Details:**
*   **File Modified:** [`app/api/oqc-query/route.ts`](app/api/oqc-query/route.ts:1)
*   **Change:** 修改了JSON解析部分，以正确处理嵌套的`Table1`键。