"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _logParser_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logParser.module */ \"(app-pages-browser)/./workers/logParser.module.ts\");\n\nlet processedLogBlocks = [];\n// 设置全局错误处理，以防有未捕获的异常\nself.addEventListener('error', (event)=>{\n    console.error('Unhandled error in worker:', event);\n    self.postMessage({\n        type: 'ERROR',\n        error: {\n            message: event.message,\n            filename: event.filename,\n            lineno: event.lineno\n        }\n    });\n});\n/**\r\n * 监听来自主线程的消息。\r\n */ self.onmessage = async (event)=>{\n    switch(event.data.type){\n        case 'PARSE_LOG':\n            {\n                const { logContent } = event.data.payload;\n                if (!logContent) {\n                    const errorMessage = {\n                        type: 'PARSE_LOG_RESULT',\n                        error: 'Invalid message format: logContent is missing in payload.'\n                    };\n                    console.error('[Worker] Posting error to main thread:', errorMessage);\n                    self.postMessage(errorMessage);\n                    return;\n                }\n                try {\n                    const processedData = await (0,_logParser_module__WEBPACK_IMPORTED_MODULE_0__.processLogFile)(logContent);\n                    processedLogBlocks = processedData.blocks; // Store processed blocks\n                    const message = {\n                        type: 'PARSE_LOG_RESULT',\n                        allBlocks: processedData.blocks\n                    };\n                    console.log('[Worker] Posting message to main thread:', message);\n                    self.postMessage(message);\n                } catch (error) {\n                    console.error('Error processing log file in worker:', error);\n                    const errorMessage = {\n                        type: 'PARSE_LOG_RESULT',\n                        error: error instanceof Error ? error.message : 'An unknown error occurred.'\n                    };\n                    console.error('[Worker] Posting error to main thread:', errorMessage);\n                    self.postMessage(errorMessage);\n                }\n                break;\n            }\n        case 'MATCH_BY_TIMESTAMP':\n            {\n                const { timestamps } = event.data.payload;\n                const matchedBlockIds = (0,_logParser_module__WEBPACK_IMPORTED_MODULE_0__.findBlocksByTimestamp)(processedLogBlocks, timestamps);\n                self.postMessage({\n                    type: 'MATCH_BY_TIMESTAMP_RESULT',\n                    payload: {\n                        matchedBlockIds\n                    }\n                });\n                break;\n            }\n        default:\n            console.error('Unknown message type:', event.data.type);\n            break;\n    }\n};\n// eslint-disable-next-line no-console\nconsole.log('Log parser worker initialized.');\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.worker.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("3be5236459f28454")
/******/ })();
/******/ 
/******/ }
);