// hotel-dashboard/workers/rulesEngine.ts

export const REGEX_TIMESTAMP = /(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2},\d{3})/;

// --- V1 Specific Rules ---
export const REGEX_V1_GLUE_THICKNESS = /####### 胶厚值:([-\d.]+)/;
export const REGEX_V1_COLLIMATION = /####### 准直diff:([-\d.]+)/;

// --- V2 Specific Rules ---
export const REGEX_V2_GLUE_THICKNESS = /INFO\s+.*Thickness:([-\d.]+)/;
export const REGEX_V2_COORDS = /点13均值x:([-\d.]+),\s*点13均值y:([-\d.]+),\s*点2x:([-\d.]+),\s*点2y:([-\d.]+)/;