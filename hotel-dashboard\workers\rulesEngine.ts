/**
 * @file hotel-dashboard/workers/rulesEngine.ts
 * @description
 * 该文件定义了日志解析规则引擎的核心正则表达式。
 *
 * **工作原理:**
 * 此文件本身不包含执行逻辑，而是作为一个集中的“规则库”。它导出一系列具名的正则表达式（`Rule`），
 * 每个表达式都设计用于从单行日志文本中精确匹配和提取特定的数据片段。
 *
 * 数据提取器（如 `dataExtractor.module.ts`）会导入这些规则，并按顺序将它们应用于日志文件的每一行。
 * 当一个规则成功匹配时，提取器会捕获匹配组中的数据（例如，一个数值或坐标），并将其与
 * 最近看到的时间戳关联起来，最终构建出结构化的数据对象。
 *
 * **`applyRules` 函数的概念:**
 * 尽管此文件中没有名为 `applyRules` 的函数，但其消费者的工作方式可以概念化为：
 *
 * ```typescript
 * function applyRules(line: string, rules: RegExp[]): Data | null {
 *   for (const rule of rules) {
 *     const match = rule.exec(line);
 *     if (match) {
 *       // 从 match[1], match[2], ... 中提取捕获组
 *       // 将提取的数据转换为结构化格式
 *       // 返回结构化数据
 *     }
 *   }
 *   return null;
 * }
 * ```
 * 这种模式将规则定义与规则应用分离开来，提高了代码的可维护性和可扩展性。
 */

/**
 * @description
 * 通用时间戳匹配规则。
 * 用于从日志行中捕获标准格式的时间戳。
 * @example "2025-07-15 14:30:00,123" -> "2025-07-15 14:30:00,123"
 * @type {RegExp}
 */
export const REGEX_TIMESTAMP = /(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2},\d{3})/;

// --- V1 Specific Rules ---

/**
 * @description
 * V1 日志格式的胶厚值提取规则。
 * 匹配以 "####### 胶厚值:" 开头的行，并捕获其后的数值。
 * @example "####### 胶厚值:1200.5" -> "1200.5"
 * @type {RegExp}
 */
export const REGEX_V1_GLUE_THICKNESS = /####### 胶厚值:([-\d.]+)/;

/**
 * @description
 * V1 日志格式的准直差异值提取规则。
 * 匹配以 "####### 准直diff:" 开头的行，并捕获其后的数值。
 * @example "####### 准直diff:0.045" -> "0.045"
 * @type {RegExp}
 */
export const REGEX_V1_COLLIMATION = /####### 准直diff:([-\d.]+)/;

// --- V2 Specific Rules ---

/**
 * @description
 * V2 日志格式的胶厚值提取规则。
 * 匹配以 "INFO" 开始且包含 "Thickness:" 的行，并捕获其后的数值。
 * @example "INFO - Thickness:1199.8" -> "1199.8"
 * @type {RegExp}
 */
export const REGEX_V2_GLUE_THICKNESS = /^INFO.*Thickness:([-\d.]+)/;

/**
 * @description
 * V2 日志格式的坐标提取规则。
 * 用于计算准直差异。它从单行中捕获点13的平均x/y坐标和点2的x/y坐标。
 * @example "点13均值x:1.1, 点13均值y:2.2, 点2x:3.3, 点2y:4.4" -> "1.1", "2.2", "3.3", "4.4"
 * @type {RegExp}
 */
export const REGEX_V2_COORDS = /点13均值x:([-\d.]+),\s*点13均值y:([-\d.]+),\s*点2x:([-\d.]+),\s*点2y:([-\d.]+)/;

/**
 * @description
 * 从 "insert into ..." SQL语句中提取数据。
 * 这个正则表达式本身并不提取单个值，而是捕获两个主要部分：
 * 1. 括号内的列名列表。
 * 2. `values` 关键字后括号内的值列表。
 *
 * **使用方法:**
 * 应用此正则表达式后，应在代码中执行以下步骤：
 * 1. 将捕获的列名字符串（第一个捕获组）按逗号分割成一个数组。
 * 2. 找到目标列（例如 `ColimatorDiff_afterUV`）在该数组中的索引。
 * 3. 将捕获的值字符串（第二个捕获组）按逗号分割成一个数组。
 * 4. 使用在步骤 2 中找到的索引从此数组中提取相应的值。
 *
 * @example "insert into h_support(SN, ..., ColimatorDiff_afterUV) values ("...", ..., 0.014)"
 *          -> match[1] = "SN, ..., ColimatorDiff_afterUV"
 *          -> match[2] = ""...", ..., 0.014"
 * @type {RegExp}
 */
export const REGEX_INSERT_AFTER_UV_DIFF = /insert into \w+\s*\(([^)]+)\)\s*values\s*\(([^)]+)\)/;