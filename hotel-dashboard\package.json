{"name": "my-v0-project", "version": "0.2.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start -H 0.0.0.0", "lint": "next lint"}, "pnpm": {"allowed-scripts": {"canvas": true}}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@types/archiver": "^6.0.3", "@types/file-saver": "^2.0.7", "@types/jszip": "^3.4.0", "@types/uuid": "^10.0.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.20", "basic-ftp": "^5.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "dom-to-image-more": "^3.6.0", "embla-carousel-react": "8.5.1", "file-saver": "^2.0.5", "input-otp": "1.4.1", "jschardet": "^3.1.4", "jszip": "^3.10.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "node-stream-zip": "^1.15.0", "react": "^18.3.1", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-zoom-pan-pinch": "^3.7.0", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.6", "zod": "^3.24.1"}, "optionalDependencies": {"canvas": "^3.1.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^9.28.0", "eslint-config-next": "15.3.3", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}