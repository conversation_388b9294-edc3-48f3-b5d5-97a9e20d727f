"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-zoom-pan-pinch";
exports.ids = ["vendor-chunks/react-zoom-pan-pinch"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-zoom-pan-pinch/dist/index.esm.js":
/*!*************************************************************!*\
  !*** ./node_modules/react-zoom-pan-pinch/dist/index.esm.js ***!
  \*************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Context: () => (/* binding */ Context),\n/* harmony export */   KeepScale: () => (/* binding */ KeepScale),\n/* harmony export */   MiniMap: () => (/* binding */ MiniMap),\n/* harmony export */   TransformComponent: () => (/* binding */ TransformComponent),\n/* harmony export */   TransformWrapper: () => (/* binding */ TransformWrapper),\n/* harmony export */   getCenterPosition: () => (/* binding */ getCenterPosition),\n/* harmony export */   getMatrixTransformStyles: () => (/* binding */ getMatrixTransformStyles),\n/* harmony export */   getTransformStyles: () => (/* binding */ getTransformStyles),\n/* harmony export */   useControls: () => (/* binding */ useControls),\n/* harmony export */   useTransformComponent: () => (/* binding */ useTransformComponent),\n/* harmony export */   useTransformContext: () => (/* binding */ useTransformContext),\n/* harmony export */   useTransformEffect: () => (/* binding */ useTransformEffect),\n/* harmony export */   useTransformInit: () => (/* binding */ useTransformInit)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\n/**\n * Rounds number to given decimal\n * eg. roundNumber(2.34343, 1) => 2.3\n */\nvar roundNumber = function (num, decimal) {\n    return Number(num.toFixed(decimal));\n};\n/**\n * Checks if value is number, if not it returns default value\n * 1# eg. checkIsNumber(2, 30) => 2\n * 2# eg. checkIsNumber(null, 30) => 30\n */\nvar checkIsNumber = function (num, defaultValue) {\n    return typeof num === \"number\" ? num : defaultValue;\n};\n\nvar handleCallback = function (context, event, callback) {\n    if (callback && typeof callback === \"function\") {\n        callback(context, event);\n    }\n};\n\n/* eslint-disable no-plusplus */\n/* eslint-disable no-param-reassign */\n/**\n * Functions should return denominator of the target value, which is the next animation step.\n * t is a value from 0 to 1, reflecting the percentage of animation status.\n */\nvar easeOut = function (t) {\n    return -Math.cos(t * Math.PI) / 2 + 0.5;\n};\n// linear\nvar linear = function (t) {\n    return t;\n};\n// accelerating from zero velocity\nvar easeInQuad = function (t) {\n    return t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuad = function (t) {\n    return t * (2 - t);\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuad = function (t) {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n};\n// accelerating from zero velocity\nvar easeInCubic = function (t) {\n    return t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutCubic = function (t) {\n    return --t * t * t + 1;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutCubic = function (t) {\n    return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n};\n// accelerating from zero velocity\nvar easeInQuart = function (t) {\n    return t * t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuart = function (t) {\n    return 1 - --t * t * t * t;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuart = function (t) {\n    return t < 0.5 ? 8 * t * t * t * t : 1 - 8 * --t * t * t * t;\n};\n// accelerating from zero velocity\nvar easeInQuint = function (t) {\n    return t * t * t * t * t;\n};\n// decelerating to zero velocity\nvar easeOutQuint = function (t) {\n    return 1 + --t * t * t * t * t;\n};\n// acceleration until halfway, then deceleration\nvar easeInOutQuint = function (t) {\n    return t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t;\n};\nvar animations = {\n    easeOut: easeOut,\n    linear: linear,\n    easeInQuad: easeInQuad,\n    easeOutQuad: easeOutQuad,\n    easeInOutQuad: easeInOutQuad,\n    easeInCubic: easeInCubic,\n    easeOutCubic: easeOutCubic,\n    easeInOutCubic: easeInOutCubic,\n    easeInQuart: easeInQuart,\n    easeOutQuart: easeOutQuart,\n    easeInOutQuart: easeInOutQuart,\n    easeInQuint: easeInQuint,\n    easeOutQuint: easeOutQuint,\n    easeInOutQuint: easeInOutQuint,\n};\n\n/* eslint-disable no-param-reassign */\nvar handleCancelAnimationFrame = function (animation) {\n    if (typeof animation === \"number\") {\n        cancelAnimationFrame(animation);\n    }\n};\nvar handleCancelAnimation = function (contextInstance) {\n    if (!contextInstance.mounted)\n        return;\n    handleCancelAnimationFrame(contextInstance.animation);\n    // Clear animation state\n    contextInstance.animate = false;\n    contextInstance.animation = null;\n    contextInstance.velocity = null;\n};\nfunction handleSetupAnimation(contextInstance, animationName, animationTime, callback) {\n    if (!contextInstance.mounted)\n        return;\n    var startTime = new Date().getTime();\n    var lastStep = 1;\n    // if another animation is active\n    handleCancelAnimation(contextInstance);\n    // new animation\n    contextInstance.animation = function () {\n        if (!contextInstance.mounted) {\n            return handleCancelAnimationFrame(contextInstance.animation);\n        }\n        var frameTime = new Date().getTime() - startTime;\n        var animationProgress = frameTime / animationTime;\n        var animationType = animations[animationName];\n        var step = animationType(animationProgress);\n        if (frameTime >= animationTime) {\n            callback(lastStep);\n            contextInstance.animation = null;\n        }\n        else if (contextInstance.animation) {\n            callback(step);\n            requestAnimationFrame(contextInstance.animation);\n        }\n    };\n    requestAnimationFrame(contextInstance.animation);\n}\nfunction isValidTargetState(targetState) {\n    var scale = targetState.scale, positionX = targetState.positionX, positionY = targetState.positionY;\n    if (Number.isNaN(scale) ||\n        Number.isNaN(positionX) ||\n        Number.isNaN(positionY)) {\n        return false;\n    }\n    return true;\n}\nfunction animate(contextInstance, targetState, animationTime, animationName) {\n    var isValid = isValidTargetState(targetState);\n    if (!contextInstance.mounted || !isValid)\n        return;\n    var setTransformState = contextInstance.setTransformState;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    var scaleDiff = targetState.scale - scale;\n    var positionXDiff = targetState.positionX - positionX;\n    var positionYDiff = targetState.positionY - positionY;\n    if (animationTime === 0) {\n        setTransformState(targetState.scale, targetState.positionX, targetState.positionY);\n    }\n    else {\n        // animation start timestamp\n        handleSetupAnimation(contextInstance, animationName, animationTime, function (step) {\n            var newScale = scale + scaleDiff * step;\n            var newPositionX = positionX + positionXDiff * step;\n            var newPositionY = positionY + positionYDiff * step;\n            setTransformState(newScale, newPositionX, newPositionY);\n        });\n    }\n}\n\n/* eslint-disable no-param-reassign */\nfunction getComponentsSizes(wrapperComponent, contentComponent, newScale) {\n    var wrapperWidth = wrapperComponent.offsetWidth;\n    var wrapperHeight = wrapperComponent.offsetHeight;\n    var contentWidth = contentComponent.offsetWidth;\n    var contentHeight = contentComponent.offsetHeight;\n    var newContentWidth = contentWidth * newScale;\n    var newContentHeight = contentHeight * newScale;\n    var newDiffWidth = wrapperWidth - newContentWidth;\n    var newDiffHeight = wrapperHeight - newContentHeight;\n    return {\n        wrapperWidth: wrapperWidth,\n        wrapperHeight: wrapperHeight,\n        newContentWidth: newContentWidth,\n        newDiffWidth: newDiffWidth,\n        newContentHeight: newContentHeight,\n        newDiffHeight: newDiffHeight,\n    };\n}\nvar getBounds = function (wrapperWidth, newContentWidth, diffWidth, wrapperHeight, newContentHeight, diffHeight, centerZoomedOut) {\n    var scaleWidthFactor = wrapperWidth > newContentWidth\n        ? diffWidth * (centerZoomedOut ? 1 : 0.5)\n        : 0;\n    var scaleHeightFactor = wrapperHeight > newContentHeight\n        ? diffHeight * (centerZoomedOut ? 1 : 0.5)\n        : 0;\n    var minPositionX = wrapperWidth - newContentWidth - scaleWidthFactor;\n    var maxPositionX = scaleWidthFactor;\n    var minPositionY = wrapperHeight - newContentHeight - scaleHeightFactor;\n    var maxPositionY = scaleHeightFactor;\n    return { minPositionX: minPositionX, maxPositionX: maxPositionX, minPositionY: minPositionY, maxPositionY: maxPositionY };\n};\nvar calculateBounds = function (contextInstance, newScale) {\n    var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n    var centerZoomedOut = contextInstance.setup.centerZoomedOut;\n    if (!wrapperComponent || !contentComponent) {\n        throw new Error(\"Components are not mounted\");\n    }\n    var _a = getComponentsSizes(wrapperComponent, contentComponent, newScale), wrapperWidth = _a.wrapperWidth, wrapperHeight = _a.wrapperHeight, newContentWidth = _a.newContentWidth, newDiffWidth = _a.newDiffWidth, newContentHeight = _a.newContentHeight, newDiffHeight = _a.newDiffHeight;\n    var bounds = getBounds(wrapperWidth, newContentWidth, newDiffWidth, wrapperHeight, newContentHeight, newDiffHeight, Boolean(centerZoomedOut));\n    return bounds;\n};\n/**\n * Keeps value between given bounds, used for limiting view to given boundaries\n * 1# eg. boundLimiter(2, 0, 3, true) => 2\n * 2# eg. boundLimiter(4, 0, 3, true) => 3\n * 3# eg. boundLimiter(-2, 0, 3, true) => 0\n * 4# eg. boundLimiter(10, 0, 3, false) => 10\n */\nvar boundLimiter = function (value, minBound, maxBound, isActive) {\n    if (!isActive)\n        return roundNumber(value, 2);\n    if (value < minBound)\n        return roundNumber(minBound, 2);\n    if (value > maxBound)\n        return roundNumber(maxBound, 2);\n    return roundNumber(value, 2);\n};\nvar handleCalculateBounds = function (contextInstance, newScale) {\n    var bounds = calculateBounds(contextInstance, newScale);\n    // Save bounds\n    contextInstance.bounds = bounds;\n    return bounds;\n};\nfunction getMouseBoundedPosition(positionX, positionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent) {\n    var minPositionX = bounds.minPositionX, minPositionY = bounds.minPositionY, maxPositionX = bounds.maxPositionX, maxPositionY = bounds.maxPositionY;\n    var paddingX = 0;\n    var paddingY = 0;\n    if (wrapperComponent) {\n        paddingX = paddingValueX;\n        paddingY = paddingValueY;\n    }\n    var x = boundLimiter(positionX, minPositionX - paddingX, maxPositionX + paddingX, limitToBounds);\n    var y = boundLimiter(positionY, minPositionY - paddingY, maxPositionY + paddingY, limitToBounds);\n    return { x: x, y: y };\n}\n\nfunction handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds) {\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    var scaleDifference = newScale - scale;\n    if (typeof mouseX !== \"number\" || typeof mouseY !== \"number\") {\n        console.error(\"Mouse X and Y position were not provided!\");\n        return { x: positionX, y: positionY };\n    }\n    var calculatedPositionX = positionX - mouseX * scaleDifference;\n    var calculatedPositionY = positionY - mouseY * scaleDifference;\n    // do not limit to bounds when there is padding animation,\n    // it causes animation strange behaviour\n    var newPositions = getMouseBoundedPosition(calculatedPositionX, calculatedPositionY, bounds, limitToBounds, 0, 0, null);\n    return newPositions;\n}\nfunction checkZoomBounds(zoom, minScale, maxScale, zoomPadding, enablePadding) {\n    var scalePadding = enablePadding ? zoomPadding : 0;\n    var minScaleWithPadding = minScale - scalePadding;\n    if (!Number.isNaN(maxScale) && zoom >= maxScale)\n        return maxScale;\n    if (!Number.isNaN(minScale) && zoom <= minScaleWithPadding)\n        return minScaleWithPadding;\n    return zoom;\n}\n\nvar isPanningStartAllowed = function (contextInstance, event) {\n    var excluded = contextInstance.setup.panning.excluded;\n    var isInitialized = contextInstance.isInitialized, wrapperComponent = contextInstance.wrapperComponent;\n    var target = event.target;\n    var targetIsShadowDom = \"shadowRoot\" in target && \"composedPath\" in event;\n    var isWrapperChild = targetIsShadowDom\n        ? event.composedPath().some(function (el) {\n            if (!(el instanceof Element)) {\n                return false;\n            }\n            return wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(el);\n        })\n        : wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);\n    var isAllowed = isInitialized && target && isWrapperChild;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar isPanningAllowed = function (contextInstance) {\n    var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning, setup = contextInstance.setup;\n    var disabled = setup.panning.disabled;\n    var isAllowed = isInitialized && isPanning && !disabled;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar handlePanningSetup = function (contextInstance, event) {\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;\n    contextInstance.isPanning = true;\n    // Panning with mouse\n    var x = event.clientX;\n    var y = event.clientY;\n    contextInstance.startCoords = { x: x - positionX, y: y - positionY };\n};\nvar handleTouchPanningSetup = function (contextInstance, event) {\n    var touches = event.touches;\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY;\n    contextInstance.isPanning = true;\n    // Panning with touch\n    var oneFingerTouch = touches.length === 1;\n    if (oneFingerTouch) {\n        var x = touches[0].clientX;\n        var y = touches[0].clientY;\n        contextInstance.startCoords = { x: x - positionX, y: y - positionY };\n    }\n};\nfunction handlePanToBounds(contextInstance) {\n    var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;\n    var _b = contextInstance.setup, disabled = _b.disabled, limitToBounds = _b.limitToBounds, centerZoomedOut = _b.centerZoomedOut;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    if (disabled || !wrapperComponent || !contextInstance.bounds)\n        return;\n    var _c = contextInstance.bounds, maxPositionX = _c.maxPositionX, minPositionX = _c.minPositionX, maxPositionY = _c.maxPositionY, minPositionY = _c.minPositionY;\n    var xChanged = positionX > maxPositionX || positionX < minPositionX;\n    var yChanged = positionY > maxPositionY || positionY < minPositionY;\n    var mousePosX = positionX > maxPositionX\n        ? wrapperComponent.offsetWidth\n        : contextInstance.setup.minPositionX || 0;\n    var mousePosY = positionY > maxPositionY\n        ? wrapperComponent.offsetHeight\n        : contextInstance.setup.minPositionY || 0;\n    var _d = handleCalculateZoomPositions(contextInstance, mousePosX, mousePosY, scale, contextInstance.bounds, limitToBounds || centerZoomedOut), x = _d.x, y = _d.y;\n    return {\n        scale: scale,\n        positionX: xChanged ? x : positionX,\n        positionY: yChanged ? y : positionY,\n    };\n}\nfunction handleNewPosition(contextInstance, newPositionX, newPositionY, paddingValueX, paddingValueY) {\n    var limitToBounds = contextInstance.setup.limitToBounds;\n    var wrapperComponent = contextInstance.wrapperComponent, bounds = contextInstance.bounds;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (wrapperComponent === null ||\n        bounds === null ||\n        (newPositionX === positionX && newPositionY === positionY)) {\n        return;\n    }\n    var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), x = _b.x, y = _b.y;\n    contextInstance.setTransformState(scale, x, y);\n}\nvar getPanningClientPosition = function (contextInstance, clientX, clientY) {\n    var startCoords = contextInstance.startCoords, transformState = contextInstance.transformState;\n    var panning = contextInstance.setup.panning;\n    var lockAxisX = panning.lockAxisX, lockAxisY = panning.lockAxisY;\n    var positionX = transformState.positionX, positionY = transformState.positionY;\n    if (!startCoords) {\n        return { x: positionX, y: positionY };\n    }\n    var mouseX = clientX - startCoords.x;\n    var mouseY = clientY - startCoords.y;\n    var newPositionX = lockAxisX ? positionX : mouseX;\n    var newPositionY = lockAxisY ? positionY : mouseY;\n    return { x: newPositionX, y: newPositionY };\n};\nvar getPaddingValue = function (contextInstance, size) {\n    var setup = contextInstance.setup, transformState = contextInstance.transformState;\n    var scale = transformState.scale;\n    var minScale = setup.minScale, disablePadding = setup.disablePadding;\n    if (size > 0 && scale >= minScale && !disablePadding) {\n        return size;\n    }\n    return 0;\n};\n\nvar isVelocityCalculationAllowed = function (contextInstance) {\n    var mounted = contextInstance.mounted;\n    var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;\n    var scale = contextInstance.transformState.scale;\n    var disabledVelocity = velocityAnimation.disabled;\n    var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar isVelocityAllowed = function (contextInstance) {\n    var mounted = contextInstance.mounted, velocity = contextInstance.velocity, bounds = contextInstance.bounds;\n    var _a = contextInstance.setup, disabled = _a.disabled, velocityAnimation = _a.velocityAnimation;\n    var scale = contextInstance.transformState.scale;\n    var disabledVelocity = velocityAnimation.disabled;\n    var isAllowed = !disabledVelocity || scale > 1 || !disabled || mounted;\n    if (!isAllowed)\n        return false;\n    if (!velocity || !bounds)\n        return false;\n    return true;\n};\nfunction getVelocityMoveTime(contextInstance, velocity) {\n    var velocityAnimation = contextInstance.setup.velocityAnimation;\n    var equalToMove = velocityAnimation.equalToMove, animationTime = velocityAnimation.animationTime, sensitivity = velocityAnimation.sensitivity;\n    if (equalToMove) {\n        return animationTime * velocity * sensitivity;\n    }\n    return animationTime;\n}\nfunction getVelocityPosition(newPosition, startPosition, currentPosition, isLocked, limitToBounds, minPosition, maxPosition, minTarget, maxTarget, step) {\n    if (limitToBounds) {\n        if (startPosition > maxPosition && currentPosition > maxPosition) {\n            var calculatedPosition = maxPosition + (newPosition - maxPosition) * step;\n            if (calculatedPosition > maxTarget)\n                return maxTarget;\n            if (calculatedPosition < maxPosition)\n                return maxPosition;\n            return calculatedPosition;\n        }\n        if (startPosition < minPosition && currentPosition < minPosition) {\n            var calculatedPosition = minPosition + (newPosition - minPosition) * step;\n            if (calculatedPosition < minTarget)\n                return minTarget;\n            if (calculatedPosition > minPosition)\n                return minPosition;\n            return calculatedPosition;\n        }\n    }\n    if (isLocked)\n        return startPosition;\n    return boundLimiter(newPosition, minPosition, maxPosition, limitToBounds);\n}\n\nfunction getSizeMultiplier(wrapperComponent, equalToMove) {\n    var defaultMultiplier = 1;\n    if (equalToMove) {\n        return Math.min(defaultMultiplier, wrapperComponent.offsetWidth / window.innerWidth);\n    }\n    return defaultMultiplier;\n}\nfunction handleCalculateVelocity(contextInstance, position) {\n    var isAllowed = isVelocityCalculationAllowed(contextInstance);\n    if (!isAllowed) {\n        return;\n    }\n    var lastMousePosition = contextInstance.lastMousePosition, velocityTime = contextInstance.velocityTime, setup = contextInstance.setup;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var equalToMove = setup.velocityAnimation.equalToMove;\n    var now = Date.now();\n    if (lastMousePosition && velocityTime && wrapperComponent) {\n        var sizeMultiplier = getSizeMultiplier(wrapperComponent, equalToMove);\n        var distanceX = position.x - lastMousePosition.x;\n        var distanceY = position.y - lastMousePosition.y;\n        var velocityX = distanceX / sizeMultiplier;\n        var velocityY = distanceY / sizeMultiplier;\n        var interval = now - velocityTime;\n        var speed = distanceX * distanceX + distanceY * distanceY;\n        var velocity = Math.sqrt(speed) / interval;\n        contextInstance.velocity = { velocityX: velocityX, velocityY: velocityY, total: velocity };\n    }\n    contextInstance.lastMousePosition = position;\n    contextInstance.velocityTime = now;\n}\nfunction handleVelocityPanning(contextInstance) {\n    var velocity = contextInstance.velocity, bounds = contextInstance.bounds, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var isAllowed = isVelocityAllowed(contextInstance);\n    if (!isAllowed || !velocity || !bounds || !wrapperComponent) {\n        return;\n    }\n    var velocityX = velocity.velocityX, velocityY = velocity.velocityY, total = velocity.total;\n    var maxPositionX = bounds.maxPositionX, minPositionX = bounds.minPositionX, maxPositionY = bounds.maxPositionY, minPositionY = bounds.minPositionY;\n    var limitToBounds = setup.limitToBounds, alignmentAnimation = setup.alignmentAnimation;\n    var zoomAnimation = setup.zoomAnimation, panning = setup.panning;\n    var lockAxisY = panning.lockAxisY, lockAxisX = panning.lockAxisX;\n    var animationType = zoomAnimation.animationType;\n    var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, velocityAlignmentTime = alignmentAnimation.velocityAlignmentTime;\n    var alignAnimationTime = velocityAlignmentTime;\n    var moveAnimationTime = getVelocityMoveTime(contextInstance, total);\n    var finalAnimationTime = Math.max(moveAnimationTime, alignAnimationTime);\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    var paddingX = (paddingValueX * wrapperComponent.offsetWidth) / 100;\n    var paddingY = (paddingValueY * wrapperComponent.offsetHeight) / 100;\n    var maxTargetX = maxPositionX + paddingX;\n    var minTargetX = minPositionX - paddingX;\n    var maxTargetY = maxPositionY + paddingY;\n    var minTargetY = minPositionY - paddingY;\n    var startState = contextInstance.transformState;\n    var startTime = new Date().getTime();\n    handleSetupAnimation(contextInstance, animationType, finalAnimationTime, function (step) {\n        var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n        var frameTime = new Date().getTime() - startTime;\n        var animationProgress = frameTime / alignAnimationTime;\n        var alignAnimation = animations[alignmentAnimation.animationType];\n        var alignStep = 1 - alignAnimation(Math.min(1, animationProgress));\n        var customStep = 1 - step;\n        var newPositionX = positionX + velocityX * customStep;\n        var newPositionY = positionY + velocityY * customStep;\n        var currentPositionX = getVelocityPosition(newPositionX, startState.positionX, positionX, lockAxisX, limitToBounds, minPositionX, maxPositionX, minTargetX, maxTargetX, alignStep);\n        var currentPositionY = getVelocityPosition(newPositionY, startState.positionY, positionY, lockAxisY, limitToBounds, minPositionY, maxPositionY, minTargetY, maxTargetY, alignStep);\n        if (positionX !== newPositionX || positionY !== newPositionY) {\n            contextInstance.setTransformState(scale, currentPositionX, currentPositionY);\n        }\n    });\n}\n\nfunction handlePanningStart(contextInstance, event) {\n    var scale = contextInstance.transformState.scale;\n    handleCancelAnimation(contextInstance);\n    handleCalculateBounds(contextInstance, scale);\n    if (window.TouchEvent !== undefined && event instanceof TouchEvent) {\n        handleTouchPanningSetup(contextInstance, event);\n    }\n    else {\n        handlePanningSetup(contextInstance, event);\n    }\n}\nfunction handleAlignToBounds(contextInstance, customAnimationTime) {\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, minScale = _a.minScale, alignmentAnimation = _a.alignmentAnimation;\n    var disabled = alignmentAnimation.disabled, sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY, animationTime = alignmentAnimation.animationTime, animationType = alignmentAnimation.animationType;\n    var isDisabled = disabled || scale < minScale || (!sizeX && !sizeY);\n    if (isDisabled)\n        return;\n    var targetState = handlePanToBounds(contextInstance);\n    if (targetState) {\n        animate(contextInstance, targetState, customAnimationTime !== null && customAnimationTime !== void 0 ? customAnimationTime : animationTime, animationType);\n    }\n}\nfunction handlePanning(contextInstance, clientX, clientY) {\n    var startCoords = contextInstance.startCoords, setup = contextInstance.setup;\n    var _a = setup.alignmentAnimation, sizeX = _a.sizeX, sizeY = _a.sizeY;\n    if (!startCoords)\n        return;\n    var _b = getPanningClientPosition(contextInstance, clientX, clientY), x = _b.x, y = _b.y;\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    handleCalculateVelocity(contextInstance, { x: x, y: y });\n    handleNewPosition(contextInstance, x, y, paddingValueX, paddingValueY);\n}\nfunction handlePanningEnd(contextInstance) {\n    if (contextInstance.isPanning) {\n        var velocityDisabled = contextInstance.setup.panning.velocityDisabled;\n        var velocity = contextInstance.velocity, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        contextInstance.isPanning = false;\n        contextInstance.animate = false;\n        contextInstance.animation = null;\n        var wrapperRect = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.getBoundingClientRect();\n        var contentRect = contentComponent === null || contentComponent === void 0 ? void 0 : contentComponent.getBoundingClientRect();\n        var wrapperWidth = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.width) || 0;\n        var wrapperHeight = (wrapperRect === null || wrapperRect === void 0 ? void 0 : wrapperRect.height) || 0;\n        var contentWidth = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.width) || 0;\n        var contentHeight = (contentRect === null || contentRect === void 0 ? void 0 : contentRect.height) || 0;\n        var isZoomed = wrapperWidth < contentWidth || wrapperHeight < contentHeight;\n        var shouldAnimate = !velocityDisabled && velocity && (velocity === null || velocity === void 0 ? void 0 : velocity.total) > 0.1 && isZoomed;\n        if (shouldAnimate) {\n            handleVelocityPanning(contextInstance);\n        }\n        else {\n            handleAlignToBounds(contextInstance);\n        }\n    }\n}\n\nfunction handleZoomToPoint(contextInstance, scale, mouseX, mouseY) {\n    var _a = contextInstance.setup, minScale = _a.minScale, maxScale = _a.maxScale, limitToBounds = _a.limitToBounds;\n    var newScale = checkZoomBounds(roundNumber(scale, 2), minScale, maxScale, 0, false);\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var _b = handleCalculateZoomPositions(contextInstance, mouseX, mouseY, newScale, bounds, limitToBounds), x = _b.x, y = _b.y;\n    return { scale: newScale, positionX: x, positionY: y };\n}\nfunction handleAlignToScaleBounds(contextInstance, mousePositionX, mousePositionY) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var _a = contextInstance.setup, minScale = _a.minScale, limitToBounds = _a.limitToBounds, zoomAnimation = _a.zoomAnimation;\n    var disabled = zoomAnimation.disabled, animationTime = zoomAnimation.animationTime, animationType = zoomAnimation.animationType;\n    var isDisabled = disabled || scale >= minScale;\n    if (scale >= 1 || limitToBounds) {\n        // fire fit to bounds animation\n        handleAlignToBounds(contextInstance);\n    }\n    if (isDisabled || !wrapperComponent || !contextInstance.mounted)\n        return;\n    var mouseX = mousePositionX || wrapperComponent.offsetWidth / 2;\n    var mouseY = mousePositionY || wrapperComponent.offsetHeight / 2;\n    var targetState = handleZoomToPoint(contextInstance, minScale, mouseX, mouseY);\n    if (targetState) {\n        animate(contextInstance, targetState, animationTime, animationType);\n    }\n}\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nfunction __spreadArray(to, from, pack) {\r\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\r\n        if (ar || !(i in from)) {\r\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\r\n            ar[i] = from[i];\r\n        }\r\n    }\r\n    return to.concat(ar || Array.prototype.slice.call(from));\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nvar initialState = {\n    previousScale: 1,\n    scale: 1,\n    positionX: 0,\n    positionY: 0,\n};\nvar initialSetup = {\n    disabled: false,\n    minPositionX: null,\n    maxPositionX: null,\n    minPositionY: null,\n    maxPositionY: null,\n    minScale: 1,\n    maxScale: 8,\n    limitToBounds: true,\n    centerZoomedOut: false,\n    centerOnInit: false,\n    disablePadding: false,\n    smooth: true,\n    wheel: {\n        step: 0.2,\n        disabled: false,\n        smoothStep: 0.001,\n        wheelDisabled: false,\n        touchPadDisabled: false,\n        activationKeys: [],\n        excluded: [],\n    },\n    panning: {\n        disabled: false,\n        velocityDisabled: false,\n        lockAxisX: false,\n        lockAxisY: false,\n        allowLeftClickPan: true,\n        allowMiddleClickPan: true,\n        allowRightClickPan: true,\n        wheelPanning: false,\n        activationKeys: [],\n        excluded: [],\n    },\n    pinch: {\n        step: 5,\n        disabled: false,\n        excluded: [],\n    },\n    doubleClick: {\n        disabled: false,\n        step: 0.7,\n        mode: \"zoomIn\",\n        animationType: \"easeOut\",\n        animationTime: 200,\n        excluded: [],\n    },\n    zoomAnimation: {\n        disabled: false,\n        size: 0.4,\n        animationTime: 200,\n        animationType: \"easeOut\",\n    },\n    alignmentAnimation: {\n        disabled: false,\n        sizeX: 100,\n        sizeY: 100,\n        animationTime: 200,\n        velocityAlignmentTime: 400,\n        animationType: \"easeOut\",\n    },\n    velocityAnimation: {\n        disabled: false,\n        sensitivity: 1,\n        animationTime: 400,\n        animationType: \"easeOut\",\n        equalToMove: true,\n    },\n};\nvar baseClasses = {\n    wrapperClass: \"react-transform-wrapper\",\n    contentClass: \"react-transform-component\",\n};\n\nvar createState = function (props) {\n    var _a, _b, _c, _d;\n    return {\n        previousScale: (_a = props.initialScale) !== null && _a !== void 0 ? _a : initialState.scale,\n        scale: (_b = props.initialScale) !== null && _b !== void 0 ? _b : initialState.scale,\n        positionX: (_c = props.initialPositionX) !== null && _c !== void 0 ? _c : initialState.positionX,\n        positionY: (_d = props.initialPositionY) !== null && _d !== void 0 ? _d : initialState.positionY,\n    };\n};\nvar createSetup = function (props) {\n    var newSetup = __assign({}, initialSetup);\n    Object.keys(props).forEach(function (key) {\n        var validValue = typeof props[key] !== \"undefined\";\n        var validParameter = typeof initialSetup[key] !== \"undefined\";\n        if (validParameter && validValue) {\n            var dataType = Object.prototype.toString.call(initialSetup[key]);\n            var isObject = dataType === \"[object Object]\";\n            var isArray = dataType === \"[object Array]\";\n            if (isObject) {\n                newSetup[key] = __assign(__assign({}, initialSetup[key]), props[key]);\n            }\n            else if (isArray) {\n                newSetup[key] = __spreadArray(__spreadArray([], initialSetup[key], true), props[key], true);\n            }\n            else {\n                newSetup[key] = props[key];\n            }\n        }\n    });\n    return newSetup;\n};\n\nvar handleCalculateButtonZoom = function (contextInstance, delta, step) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, smooth = setup.smooth;\n    var size = zoomAnimation.size;\n    if (!wrapperComponent) {\n        throw new Error(\"Wrapper is not mounted\");\n    }\n    var targetScale = smooth\n        ? scale * Math.exp(delta * step)\n        : scale + delta * step;\n    var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, false);\n    return newScale;\n};\nfunction handleZoomToViewCenter(contextInstance, delta, step, animationTime, animationType) {\n    var wrapperComponent = contextInstance.wrapperComponent;\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (!wrapperComponent)\n        return console.error(\"No WrapperComponent found\");\n    var wrapperWidth = wrapperComponent.offsetWidth;\n    var wrapperHeight = wrapperComponent.offsetHeight;\n    var mouseX = (wrapperWidth / 2 - positionX) / scale;\n    var mouseY = (wrapperHeight / 2 - positionY) / scale;\n    var newScale = handleCalculateButtonZoom(contextInstance, delta, step);\n    var targetState = handleZoomToPoint(contextInstance, newScale, mouseX, mouseY);\n    if (!targetState) {\n        return console.error(\"Error during zoom event. New transformation state was not calculated.\");\n    }\n    animate(contextInstance, targetState, animationTime, animationType);\n}\nfunction resetTransformations(contextInstance, animationTime, animationType, onResetTransformation) {\n    var setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var limitToBounds = setup.limitToBounds;\n    var initialTransformation = createState(contextInstance.props);\n    var _a = contextInstance.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n    if (!wrapperComponent)\n        return;\n    var newBounds = calculateBounds(contextInstance, initialTransformation.scale);\n    var boundedPositions = getMouseBoundedPosition(initialTransformation.positionX, initialTransformation.positionY, newBounds, limitToBounds, 0, 0, wrapperComponent);\n    var newState = {\n        scale: initialTransformation.scale,\n        positionX: boundedPositions.x,\n        positionY: boundedPositions.y,\n    };\n    if (scale === initialTransformation.scale &&\n        positionX === initialTransformation.positionX &&\n        positionY === initialTransformation.positionY) {\n        return;\n    }\n    onResetTransformation === null || onResetTransformation === void 0 ? void 0 : onResetTransformation();\n    animate(contextInstance, newState, animationTime, animationType);\n}\nfunction getOffset(element, wrapper, content, state) {\n    var offset = element.getBoundingClientRect();\n    var wrapperOffset = wrapper.getBoundingClientRect();\n    var contentOffset = content.getBoundingClientRect();\n    var xOff = wrapperOffset.x * state.scale;\n    var yOff = wrapperOffset.y * state.scale;\n    return {\n        x: (offset.x - contentOffset.x + xOff) / state.scale,\n        y: (offset.y - contentOffset.y + yOff) / state.scale,\n    };\n}\nfunction calculateZoomToNode(contextInstance, node, customZoom) {\n    var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent, transformState = contextInstance.transformState;\n    var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, minScale = _a.minScale, maxScale = _a.maxScale;\n    if (!wrapperComponent || !contentComponent)\n        return transformState;\n    var wrapperRect = wrapperComponent.getBoundingClientRect();\n    var nodeRect = node.getBoundingClientRect();\n    var nodeOffset = getOffset(node, wrapperComponent, contentComponent, transformState);\n    var nodeLeft = nodeOffset.x;\n    var nodeTop = nodeOffset.y;\n    var nodeWidth = nodeRect.width / transformState.scale;\n    var nodeHeight = nodeRect.height / transformState.scale;\n    var scaleX = wrapperComponent.offsetWidth / nodeWidth;\n    var scaleY = wrapperComponent.offsetHeight / nodeHeight;\n    var newScale = checkZoomBounds(customZoom || Math.min(scaleX, scaleY), minScale, maxScale, 0, false);\n    var offsetX = (wrapperRect.width - nodeWidth * newScale) / 2;\n    var offsetY = (wrapperRect.height - nodeHeight * newScale) / 2;\n    var newPositionX = (wrapperRect.left - nodeLeft) * newScale + offsetX;\n    var newPositionY = (wrapperRect.top - nodeTop) * newScale + offsetY;\n    var bounds = calculateBounds(contextInstance, newScale);\n    var _b = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, 0, 0, wrapperComponent), x = _b.x, y = _b.y;\n    return { positionX: x, positionY: y, scale: newScale };\n}\n\nvar zoomIn = function (contextInstance) {\n    return function (step, animationTime, animationType) {\n        if (step === void 0) { step = 0.5; }\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleZoomToViewCenter(contextInstance, 1, step, animationTime, animationType);\n    };\n};\nvar zoomOut = function (contextInstance) {\n    return function (step, animationTime, animationType) {\n        if (step === void 0) { step = 0.5; }\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleZoomToViewCenter(contextInstance, -1, step, animationTime, animationType);\n    };\n};\nvar setTransform = function (contextInstance) {\n    return function (newPositionX, newPositionY, newScale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 300; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        var _a = contextInstance.transformState, positionX = _a.positionX, positionY = _a.positionY, scale = _a.scale;\n        var wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        var disabled = contextInstance.setup.disabled;\n        if (disabled || !wrapperComponent || !contentComponent)\n            return;\n        var targetState = {\n            positionX: Number.isNaN(newPositionX) ? positionX : newPositionX,\n            positionY: Number.isNaN(newPositionY) ? positionY : newPositionY,\n            scale: Number.isNaN(newScale) ? scale : newScale,\n        };\n        animate(contextInstance, targetState, animationTime, animationType);\n    };\n};\nvar resetTransform = function (contextInstance) {\n    return function (animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 200; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        resetTransformations(contextInstance, animationTime, animationType);\n    };\n};\nvar centerView = function (contextInstance) {\n    return function (scale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 200; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        var transformState = contextInstance.transformState, wrapperComponent = contextInstance.wrapperComponent, contentComponent = contextInstance.contentComponent;\n        if (wrapperComponent && contentComponent) {\n            var targetState = getCenterPosition(scale || transformState.scale, wrapperComponent, contentComponent);\n            animate(contextInstance, targetState, animationTime, animationType);\n        }\n    };\n};\nvar zoomToElement = function (contextInstance) {\n    return function (node, scale, animationTime, animationType) {\n        if (animationTime === void 0) { animationTime = 600; }\n        if (animationType === void 0) { animationType = \"easeOut\"; }\n        handleCancelAnimation(contextInstance);\n        var wrapperComponent = contextInstance.wrapperComponent;\n        var target = typeof node === \"string\" ? document.getElementById(node) : node;\n        if (wrapperComponent && target && wrapperComponent.contains(target)) {\n            var targetState = calculateZoomToNode(contextInstance, target, scale);\n            animate(contextInstance, targetState, animationTime, animationType);\n        }\n    };\n};\n\nvar getControls = function (contextInstance) {\n    return {\n        instance: contextInstance,\n        zoomIn: zoomIn(contextInstance),\n        zoomOut: zoomOut(contextInstance),\n        setTransform: setTransform(contextInstance),\n        resetTransform: resetTransform(contextInstance),\n        centerView: centerView(contextInstance),\n        zoomToElement: zoomToElement(contextInstance),\n    };\n};\nvar getState = function (contextInstance) {\n    return {\n        instance: contextInstance,\n        state: contextInstance.transformState,\n    };\n};\nvar getContext = function (contextInstance) {\n    var ref = {};\n    Object.assign(ref, getState(contextInstance));\n    Object.assign(ref, getControls(contextInstance));\n    return ref;\n};\n\n// We want to make event listeners non-passive, and to do so have to check\n// that browsers support EventListenerOptions in the first place.\n// https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#Safely_detecting_option_support\nvar passiveSupported = false;\nfunction makePassiveEventOption() {\n    try {\n        var options = {\n            get passive() {\n                // This function will be called when the browser\n                //   attempts to access the passive property.\n                passiveSupported = true;\n                return false;\n            },\n        };\n        return options;\n    }\n    catch (err) {\n        passiveSupported = false;\n        return passiveSupported;\n    }\n}\n\nvar matchPrefix = \".\".concat(baseClasses.wrapperClass);\nvar isExcludedNode = function (node, excluded) {\n    return excluded.some(function (exclude) {\n        return node.matches(\"\".concat(matchPrefix, \" \").concat(exclude, \", \").concat(matchPrefix, \" .\").concat(exclude, \", \").concat(matchPrefix, \" \").concat(exclude, \" *, \").concat(matchPrefix, \" .\").concat(exclude, \" *\"));\n    });\n};\nvar cancelTimeout = function (timeout) {\n    if (timeout) {\n        clearTimeout(timeout);\n    }\n};\n\nvar getTransformStyles = function (x, y, scale) {\n    // Standard translate prevents blurry svg on the safari\n    return \"translate(\".concat(x, \"px, \").concat(y, \"px) scale(\").concat(scale, \")\");\n};\nvar getMatrixTransformStyles = function (x, y, scale) {\n    // The shorthand for matrix does not work for Safari hence the need to explicitly use matrix3d\n    // Refer to https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    var a = scale;\n    var b = 0;\n    var c = 0;\n    var d = scale;\n    var tx = x;\n    var ty = y;\n    return \"matrix3d(\".concat(a, \", \").concat(b, \", 0, 0, \").concat(c, \", \").concat(d, \", 0, 0, 0, 0, 1, 0, \").concat(tx, \", \").concat(ty, \", 0, 1)\");\n};\nvar getCenterPosition = function (scale, wrapperComponent, contentComponent) {\n    var contentWidth = contentComponent.offsetWidth * scale;\n    var contentHeight = contentComponent.offsetHeight * scale;\n    var centerPositionX = (wrapperComponent.offsetWidth - contentWidth) / 2;\n    var centerPositionY = (wrapperComponent.offsetHeight - contentHeight) / 2;\n    return {\n        scale: scale,\n        positionX: centerPositionX,\n        positionY: centerPositionY,\n    };\n};\n\nfunction mergeRefs(refs) {\n    return function (value) {\n        refs.forEach(function (ref) {\n            if (typeof ref === \"function\") {\n                ref(value);\n            }\n            else if (ref != null) {\n                ref.current = value;\n            }\n        });\n    };\n}\n\nvar isWheelAllowed = function (contextInstance, event) {\n    var _a = contextInstance.setup.wheel, disabled = _a.disabled, wheelDisabled = _a.wheelDisabled, touchPadDisabled = _a.touchPadDisabled, excluded = _a.excluded;\n    var isInitialized = contextInstance.isInitialized, isPanning = contextInstance.isPanning;\n    var target = event.target;\n    var isAllowed = isInitialized && !isPanning && !disabled && target;\n    if (!isAllowed)\n        return false;\n    // Event ctrlKey detects if touchpad action is executing wheel or pinch gesture\n    if (wheelDisabled && !event.ctrlKey)\n        return false;\n    if (touchPadDisabled && event.ctrlKey)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar getDeltaY = function (event) {\n    if (event) {\n        return event.deltaY < 0 ? 1 : -1;\n    }\n    return 0;\n};\nfunction getDelta(event, customDelta) {\n    var deltaY = getDeltaY(event);\n    var delta = checkIsNumber(customDelta, deltaY);\n    return delta;\n}\nfunction getMousePosition(event, contentComponent, scale) {\n    var contentRect = contentComponent.getBoundingClientRect();\n    var mouseX = 0;\n    var mouseY = 0;\n    if (\"clientX\" in event) {\n        // mouse position x, y over wrapper component\n        mouseX = (event.clientX - contentRect.left) / scale;\n        mouseY = (event.clientY - contentRect.top) / scale;\n    }\n    else {\n        var touch = event.touches[0];\n        mouseX = (touch.clientX - contentRect.left) / scale;\n        mouseY = (touch.clientY - contentRect.top) / scale;\n    }\n    if (Number.isNaN(mouseX) || Number.isNaN(mouseY))\n        console.error(\"No mouse or touch offset found\");\n    return {\n        x: mouseX,\n        y: mouseY,\n    };\n}\nvar handleCalculateWheelZoom = function (contextInstance, delta, step, disable, getTarget) {\n    var scale = contextInstance.transformState.scale;\n    var wrapperComponent = contextInstance.wrapperComponent, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    if (!wrapperComponent) {\n        throw new Error(\"Wrapper is not mounted\");\n    }\n    var targetScale = scale + delta * step;\n    if (getTarget)\n        return targetScale;\n    var paddingEnabled = disable ? false : !disabled;\n    var newScale = checkZoomBounds(roundNumber(targetScale, 3), minScale, maxScale, size, paddingEnabled && !disablePadding);\n    return newScale;\n};\nvar handleWheelZoomStop = function (contextInstance, event) {\n    var previousWheelEvent = contextInstance.previousWheelEvent;\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, maxScale = _a.maxScale, minScale = _a.minScale;\n    if (!previousWheelEvent)\n        return false;\n    if (scale < maxScale || scale > minScale)\n        return true;\n    if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))\n        return true;\n    if (previousWheelEvent.deltaY > 0 && previousWheelEvent.deltaY < event.deltaY)\n        return true;\n    if (previousWheelEvent.deltaY < 0 && previousWheelEvent.deltaY > event.deltaY)\n        return true;\n    if (Math.sign(previousWheelEvent.deltaY) !== Math.sign(event.deltaY))\n        return true;\n    return false;\n};\n\nvar isPinchStartAllowed = function (contextInstance, event) {\n    var _a = contextInstance.setup.pinch, disabled = _a.disabled, excluded = _a.excluded;\n    var isInitialized = contextInstance.isInitialized;\n    var target = event.target;\n    var isAllowed = isInitialized && !disabled && target;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\nvar isPinchAllowed = function (contextInstance) {\n    var disabled = contextInstance.setup.pinch.disabled;\n    var isInitialized = contextInstance.isInitialized, pinchStartDistance = contextInstance.pinchStartDistance;\n    var isAllowed = isInitialized && !disabled && pinchStartDistance;\n    if (!isAllowed)\n        return false;\n    return true;\n};\nvar calculateTouchMidPoint = function (event, scale, contentComponent) {\n    var contentRect = contentComponent.getBoundingClientRect();\n    var touches = event.touches;\n    var firstPointX = roundNumber(touches[0].clientX - contentRect.left, 5);\n    var firstPointY = roundNumber(touches[0].clientY - contentRect.top, 5);\n    var secondPointX = roundNumber(touches[1].clientX - contentRect.left, 5);\n    var secondPointY = roundNumber(touches[1].clientY - contentRect.top, 5);\n    return {\n        x: (firstPointX + secondPointX) / 2 / scale,\n        y: (firstPointY + secondPointY) / 2 / scale,\n    };\n};\nvar getTouchDistance = function (event) {\n    return Math.sqrt(Math.pow((event.touches[0].pageX - event.touches[1].pageX), 2) +\n        Math.pow((event.touches[0].pageY - event.touches[1].pageY), 2));\n};\nvar calculatePinchZoom = function (contextInstance, currentDistance) {\n    var pinchStartScale = contextInstance.pinchStartScale, pinchStartDistance = contextInstance.pinchStartDistance, setup = contextInstance.setup;\n    var maxScale = setup.maxScale, minScale = setup.minScale, zoomAnimation = setup.zoomAnimation, disablePadding = setup.disablePadding;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    if (!pinchStartScale || pinchStartDistance === null || !currentDistance) {\n        throw new Error(\"Pinch touches distance was not provided\");\n    }\n    if (currentDistance < 0) {\n        return contextInstance.transformState.scale;\n    }\n    var touchProportion = currentDistance / pinchStartDistance;\n    var scaleDifference = touchProportion * pinchStartScale;\n    return checkZoomBounds(roundNumber(scaleDifference, 2), minScale, maxScale, size, !disabled && !disablePadding);\n};\n\nvar wheelStopEventTime = 160;\nvar wheelAnimationTime = 100;\nvar handleWheelStart = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheelStart = _a.onWheelStart, onZoomStart = _a.onZoomStart;\n    if (!contextInstance.wheelStopEventTimer) {\n        handleCancelAnimation(contextInstance);\n        handleCallback(getContext(contextInstance), event, onWheelStart);\n        handleCallback(getContext(contextInstance), event, onZoomStart);\n    }\n};\nvar handleWheelZoom = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheel = _a.onWheel, onZoom = _a.onZoom;\n    var contentComponent = contextInstance.contentComponent, setup = contextInstance.setup, transformState = contextInstance.transformState;\n    var scale = transformState.scale;\n    var limitToBounds = setup.limitToBounds, centerZoomedOut = setup.centerZoomedOut, zoomAnimation = setup.zoomAnimation, wheel = setup.wheel, disablePadding = setup.disablePadding, smooth = setup.smooth;\n    var size = zoomAnimation.size, disabled = zoomAnimation.disabled;\n    var step = wheel.step, smoothStep = wheel.smoothStep;\n    if (!contentComponent) {\n        throw new Error(\"Component not mounted\");\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    var delta = getDelta(event, null);\n    var zoomStep = smooth ? smoothStep * Math.abs(event.deltaY) : step;\n    var newScale = handleCalculateWheelZoom(contextInstance, delta, zoomStep, !event.ctrlKey);\n    // if scale not change\n    if (scale === newScale)\n        return;\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var mousePosition = getMousePosition(event, contentComponent, scale);\n    var isPaddingDisabled = disabled || size === 0 || centerZoomedOut || disablePadding;\n    var isLimitedToBounds = limitToBounds && isPaddingDisabled;\n    var _b = handleCalculateZoomPositions(contextInstance, mousePosition.x, mousePosition.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;\n    contextInstance.previousWheelEvent = event;\n    contextInstance.setTransformState(newScale, x, y);\n    handleCallback(getContext(contextInstance), event, onWheel);\n    handleCallback(getContext(contextInstance), event, onZoom);\n};\nvar handleWheelStop = function (contextInstance, event) {\n    var _a = contextInstance.props, onWheelStop = _a.onWheelStop, onZoomStop = _a.onZoomStop;\n    // fire animation\n    cancelTimeout(contextInstance.wheelAnimationTimer);\n    contextInstance.wheelAnimationTimer = setTimeout(function () {\n        if (!contextInstance.mounted)\n            return;\n        handleAlignToScaleBounds(contextInstance, event.x, event.y);\n        contextInstance.wheelAnimationTimer = null;\n    }, wheelAnimationTime);\n    // Wheel stop event\n    var hasStoppedZooming = handleWheelZoomStop(contextInstance, event);\n    if (hasStoppedZooming) {\n        cancelTimeout(contextInstance.wheelStopEventTimer);\n        contextInstance.wheelStopEventTimer = setTimeout(function () {\n            if (!contextInstance.mounted)\n                return;\n            contextInstance.wheelStopEventTimer = null;\n            handleCallback(getContext(contextInstance), event, onWheelStop);\n            handleCallback(getContext(contextInstance), event, onZoomStop);\n        }, wheelStopEventTime);\n    }\n};\n\nvar getTouchCenter = function (event) {\n    var totalX = 0;\n    var totalY = 0;\n    // Sum up the positions of all touches\n    for (var i = 0; i < 2; i += 1) {\n        totalX += event.touches[i].clientX;\n        totalY += event.touches[i].clientY;\n    }\n    // Calculate the average position\n    var x = totalX / 2;\n    var y = totalY / 2;\n    return { x: x, y: y };\n};\nvar handlePinchStart = function (contextInstance, event) {\n    var distance = getTouchDistance(event);\n    contextInstance.pinchStartDistance = distance;\n    contextInstance.lastDistance = distance;\n    contextInstance.pinchStartScale = contextInstance.transformState.scale;\n    contextInstance.isPanning = false;\n    var center = getTouchCenter(event);\n    contextInstance.pinchLastCenterX = center.x;\n    contextInstance.pinchLastCenterY = center.y;\n    handleCancelAnimation(contextInstance);\n};\nvar handlePinchZoom = function (contextInstance, event) {\n    var contentComponent = contextInstance.contentComponent, pinchStartDistance = contextInstance.pinchStartDistance, wrapperComponent = contextInstance.wrapperComponent;\n    var scale = contextInstance.transformState.scale;\n    var _a = contextInstance.setup, limitToBounds = _a.limitToBounds, centerZoomedOut = _a.centerZoomedOut, zoomAnimation = _a.zoomAnimation, alignmentAnimation = _a.alignmentAnimation;\n    var disabled = zoomAnimation.disabled, size = zoomAnimation.size;\n    // if one finger starts from outside of wrapper\n    if (pinchStartDistance === null || !contentComponent)\n        return;\n    var midPoint = calculateTouchMidPoint(event, scale, contentComponent);\n    // if touches goes off of the wrapper element\n    if (!Number.isFinite(midPoint.x) || !Number.isFinite(midPoint.y))\n        return;\n    var currentDistance = getTouchDistance(event);\n    var newScale = calculatePinchZoom(contextInstance, currentDistance);\n    var center = getTouchCenter(event);\n    // pan should be scale invariant.\n    var panX = center.x - (contextInstance.pinchLastCenterX || 0);\n    var panY = center.y - (contextInstance.pinchLastCenterY || 0);\n    if (newScale === scale && panX === 0 && panY === 0)\n        return;\n    contextInstance.pinchLastCenterX = center.x;\n    contextInstance.pinchLastCenterY = center.y;\n    var bounds = handleCalculateBounds(contextInstance, newScale);\n    var isPaddingDisabled = disabled || size === 0 || centerZoomedOut;\n    var isLimitedToBounds = limitToBounds && isPaddingDisabled;\n    var _b = handleCalculateZoomPositions(contextInstance, midPoint.x, midPoint.y, newScale, bounds, isLimitedToBounds), x = _b.x, y = _b.y;\n    contextInstance.pinchMidpoint = midPoint;\n    contextInstance.lastDistance = currentDistance;\n    var sizeX = alignmentAnimation.sizeX, sizeY = alignmentAnimation.sizeY;\n    var paddingValueX = getPaddingValue(contextInstance, sizeX);\n    var paddingValueY = getPaddingValue(contextInstance, sizeY);\n    var newPositionX = x + panX;\n    var newPositionY = y + panY;\n    var _c = getMouseBoundedPosition(newPositionX, newPositionY, bounds, limitToBounds, paddingValueX, paddingValueY, wrapperComponent), finalX = _c.x, finalY = _c.y;\n    contextInstance.setTransformState(newScale, finalX, finalY);\n};\nvar handlePinchStop = function (contextInstance) {\n    var pinchMidpoint = contextInstance.pinchMidpoint;\n    contextInstance.velocity = null;\n    contextInstance.lastDistance = null;\n    contextInstance.pinchMidpoint = null;\n    contextInstance.pinchStartScale = null;\n    contextInstance.pinchStartDistance = null;\n    handleAlignToScaleBounds(contextInstance, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.x, pinchMidpoint === null || pinchMidpoint === void 0 ? void 0 : pinchMidpoint.y);\n};\n\nvar handleDoubleClickStop = function (contextInstance, event) {\n    var onZoomStop = contextInstance.props.onZoomStop;\n    var animationTime = contextInstance.setup.doubleClick.animationTime;\n    cancelTimeout(contextInstance.doubleClickStopEventTimer);\n    contextInstance.doubleClickStopEventTimer = setTimeout(function () {\n        contextInstance.doubleClickStopEventTimer = null;\n        handleCallback(getContext(contextInstance), event, onZoomStop);\n    }, animationTime);\n};\nvar handleDoubleClickResetMode = function (contextInstance, event) {\n    var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;\n    var _b = contextInstance.setup.doubleClick, animationTime = _b.animationTime, animationType = _b.animationType;\n    handleCallback(getContext(contextInstance), event, onZoomStart);\n    resetTransformations(contextInstance, animationTime, animationType, function () {\n        return handleCallback(getContext(contextInstance), event, onZoom);\n    });\n    handleDoubleClickStop(contextInstance, event);\n};\nfunction getDoubleClickScale(mode, scale) {\n    if (mode === \"toggle\") {\n        return scale === 1 ? 1 : -1;\n    }\n    return mode === \"zoomOut\" ? -1 : 1;\n}\nfunction handleDoubleClick(contextInstance, event) {\n    var setup = contextInstance.setup, doubleClickStopEventTimer = contextInstance.doubleClickStopEventTimer, transformState = contextInstance.transformState, contentComponent = contextInstance.contentComponent;\n    var scale = transformState.scale;\n    var _a = contextInstance.props, onZoomStart = _a.onZoomStart, onZoom = _a.onZoom;\n    var _b = setup.doubleClick, disabled = _b.disabled, mode = _b.mode, step = _b.step, animationTime = _b.animationTime, animationType = _b.animationType;\n    if (disabled)\n        return;\n    if (doubleClickStopEventTimer)\n        return;\n    if (mode === \"reset\") {\n        return handleDoubleClickResetMode(contextInstance, event);\n    }\n    if (!contentComponent)\n        return console.error(\"No ContentComponent found\");\n    var delta = getDoubleClickScale(mode, contextInstance.transformState.scale);\n    var newScale = handleCalculateButtonZoom(contextInstance, delta, step);\n    // stop execution when scale didn't change\n    if (scale === newScale)\n        return;\n    handleCallback(getContext(contextInstance), event, onZoomStart);\n    var mousePosition = getMousePosition(event, contentComponent, scale);\n    var targetState = handleZoomToPoint(contextInstance, newScale, mousePosition.x, mousePosition.y);\n    if (!targetState) {\n        return console.error(\"Error during zoom event. New transformation state was not calculated.\");\n    }\n    handleCallback(getContext(contextInstance), event, onZoom);\n    animate(contextInstance, targetState, animationTime, animationType);\n    handleDoubleClickStop(contextInstance, event);\n}\nvar isDoubleClickAllowed = function (contextInstance, event) {\n    var isInitialized = contextInstance.isInitialized, setup = contextInstance.setup, wrapperComponent = contextInstance.wrapperComponent;\n    var _a = setup.doubleClick, disabled = _a.disabled, excluded = _a.excluded;\n    var target = event.target;\n    var isWrapperChild = wrapperComponent === null || wrapperComponent === void 0 ? void 0 : wrapperComponent.contains(target);\n    var isAllowed = isInitialized && target && isWrapperChild && !disabled;\n    if (!isAllowed)\n        return false;\n    var isExcluded = isExcludedNode(target, excluded);\n    if (isExcluded)\n        return false;\n    return true;\n};\n\nvar ZoomPanPinch = /** @class */ (function () {\n    function ZoomPanPinch(props) {\n        var _this = this;\n        this.mounted = true;\n        this.pinchLastCenterX = null;\n        this.pinchLastCenterY = null;\n        this.onChangeCallbacks = new Set();\n        this.onInitCallbacks = new Set();\n        // Components\n        this.wrapperComponent = null;\n        this.contentComponent = null;\n        // Initialization\n        this.isInitialized = false;\n        this.bounds = null;\n        // wheel helpers\n        this.previousWheelEvent = null;\n        this.wheelStopEventTimer = null;\n        this.wheelAnimationTimer = null;\n        // panning helpers\n        this.isPanning = false;\n        this.isWheelPanning = false;\n        this.startCoords = null;\n        this.lastTouch = null;\n        // pinch helpers\n        this.distance = null;\n        this.lastDistance = null;\n        this.pinchStartDistance = null;\n        this.pinchStartScale = null;\n        this.pinchMidpoint = null;\n        // double click helpers\n        this.doubleClickStopEventTimer = null;\n        // velocity helpers\n        this.velocity = null;\n        this.velocityTime = null;\n        this.lastMousePosition = null;\n        // animations helpers\n        this.animate = false;\n        this.animation = null;\n        this.maxBounds = null;\n        // key press\n        this.pressedKeys = {};\n        this.mount = function () {\n            _this.initializeWindowEvents();\n        };\n        this.unmount = function () {\n            _this.cleanupWindowEvents();\n        };\n        this.update = function (newProps) {\n            _this.props = newProps;\n            handleCalculateBounds(_this, _this.transformState.scale);\n            _this.setup = createSetup(newProps);\n        };\n        this.initializeWindowEvents = function () {\n            var _a, _b;\n            var passive = makePassiveEventOption();\n            var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;\n            var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;\n            (_b = _this.wrapperComponent) === null || _b === void 0 ? void 0 : _b.addEventListener(\"wheel\", _this.onWheelPanning, passive);\n            // Panning on window to allow panning when mouse is out of component wrapper\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mousedown\", _this.onPanningStart, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mousemove\", _this.onPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"mouseup\", _this.onPanningStop, passive);\n            currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.addEventListener(\"mouseleave\", _this.clearPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"keyup\", _this.setKeyUnPressed, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.addEventListener(\"keydown\", _this.setKeyPressed, passive);\n        };\n        this.cleanupWindowEvents = function () {\n            var _a, _b;\n            var passive = makePassiveEventOption();\n            var currentDocument = (_a = _this.wrapperComponent) === null || _a === void 0 ? void 0 : _a.ownerDocument;\n            var currentWindow = currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.defaultView;\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mousedown\", _this.onPanningStart, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mousemove\", _this.onPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"mouseup\", _this.onPanningStop, passive);\n            currentDocument === null || currentDocument === void 0 ? void 0 : currentDocument.removeEventListener(\"mouseleave\", _this.clearPanning, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"keyup\", _this.setKeyUnPressed, passive);\n            currentWindow === null || currentWindow === void 0 ? void 0 : currentWindow.removeEventListener(\"keydown\", _this.setKeyPressed, passive);\n            document.removeEventListener(\"mouseleave\", _this.clearPanning, passive);\n            handleCancelAnimation(_this);\n            (_b = _this.observer) === null || _b === void 0 ? void 0 : _b.disconnect();\n        };\n        this.handleInitializeWrapperEvents = function (wrapper) {\n            // Zooming events on wrapper\n            var passive = makePassiveEventOption();\n            wrapper.addEventListener(\"wheel\", _this.onWheelZoom, passive);\n            wrapper.addEventListener(\"dblclick\", _this.onDoubleClick, passive);\n            wrapper.addEventListener(\"touchstart\", _this.onTouchPanningStart, passive);\n            wrapper.addEventListener(\"touchmove\", _this.onTouchPanning, passive);\n            wrapper.addEventListener(\"touchend\", _this.onTouchPanningStop, passive);\n        };\n        this.handleInitialize = function (wrapper, contentComponent) {\n            var isCentered = false;\n            var centerOnInit = _this.setup.centerOnInit;\n            var hasTarget = function (entries, target) {\n                // eslint-disable-next-line no-restricted-syntax\n                for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                    var entry = entries_1[_i];\n                    if (entry.target === target) {\n                        return true;\n                    }\n                }\n                return false;\n            };\n            _this.applyTransformation();\n            _this.onInitCallbacks.forEach(function (callback) {\n                callback(getContext(_this));\n            });\n            _this.observer = new ResizeObserver(function (entries) {\n                if (hasTarget(entries, wrapper) || hasTarget(entries, contentComponent)) {\n                    if (centerOnInit && !isCentered) {\n                        var currentWidth = contentComponent.offsetWidth;\n                        var currentHeight = contentComponent.offsetHeight;\n                        if (currentWidth > 0 || currentHeight > 0) {\n                            isCentered = true;\n                            _this.setCenter();\n                        }\n                    }\n                    else {\n                        handleCancelAnimation(_this);\n                        handleCalculateBounds(_this, _this.transformState.scale);\n                        handleAlignToBounds(_this, 0);\n                    }\n                }\n            });\n            // Start observing the target node for configured mutations\n            _this.observer.observe(wrapper);\n            _this.observer.observe(contentComponent);\n        };\n        /// ///////\n        // Zoom\n        /// ///////\n        this.onWheelZoom = function (event) {\n            var disabled = _this.setup.disabled;\n            if (disabled)\n                return;\n            var isAllowed = isWheelAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.wheel.activationKeys);\n            if (!keysPressed)\n                return;\n            handleWheelStart(_this, event);\n            handleWheelZoom(_this, event);\n            handleWheelStop(_this, event);\n        };\n        /// ///////\n        // Pan\n        /// ///////\n        this.onWheelPanning = function (event) {\n            var _a = _this.setup, disabled = _a.disabled, wheel = _a.wheel, panning = _a.panning;\n            if (!_this.wrapperComponent ||\n                !_this.contentComponent ||\n                disabled ||\n                !wheel.wheelDisabled ||\n                panning.disabled ||\n                !panning.wheelPanning ||\n                event.ctrlKey) {\n                return;\n            }\n            event.preventDefault();\n            event.stopPropagation();\n            var _b = _this.transformState, positionX = _b.positionX, positionY = _b.positionY;\n            var mouseX = positionX - event.deltaX;\n            var mouseY = positionY - event.deltaY;\n            var newPositionX = panning.lockAxisX ? positionX : mouseX;\n            var newPositionY = panning.lockAxisY ? positionY : mouseY;\n            var _c = _this.setup.alignmentAnimation, sizeX = _c.sizeX, sizeY = _c.sizeY;\n            var paddingValueX = getPaddingValue(_this, sizeX);\n            var paddingValueY = getPaddingValue(_this, sizeY);\n            if (newPositionX === positionX && newPositionY === positionY)\n                return;\n            handleNewPosition(_this, newPositionX, newPositionY, paddingValueX, paddingValueY);\n        };\n        this.onPanningStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanningStart = _this.props.onPanningStart;\n            if (disabled)\n                return;\n            var isAllowed = isPanningStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);\n            if (!keysPressed)\n                return;\n            if (event.button === 0 && !_this.setup.panning.allowLeftClickPan)\n                return;\n            if (event.button === 1 && !_this.setup.panning.allowMiddleClickPan)\n                return;\n            if (event.button === 2 && !_this.setup.panning.allowRightClickPan)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handleCancelAnimation(_this);\n            handlePanningStart(_this, event);\n            handleCallback(getContext(_this), event, onPanningStart);\n        };\n        this.onPanning = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanning = _this.props.onPanning;\n            if (disabled)\n                return;\n            var isAllowed = isPanningAllowed(_this);\n            if (!isAllowed)\n                return;\n            var keysPressed = _this.isPressingKeys(_this.setup.panning.activationKeys);\n            if (!keysPressed)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handlePanning(_this, event.clientX, event.clientY);\n            handleCallback(getContext(_this), event, onPanning);\n        };\n        this.onPanningStop = function (event) {\n            var onPanningStop = _this.props.onPanningStop;\n            if (_this.isPanning) {\n                handlePanningEnd(_this);\n                handleCallback(getContext(_this), event, onPanningStop);\n            }\n        };\n        /// ///////\n        // Pinch\n        /// ///////\n        this.onPinchStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var _a = _this.props, onPinchingStart = _a.onPinchingStart, onZoomStart = _a.onZoomStart;\n            if (disabled)\n                return;\n            var isAllowed = isPinchStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            handlePinchStart(_this, event);\n            handleCancelAnimation(_this);\n            handleCallback(getContext(_this), event, onPinchingStart);\n            handleCallback(getContext(_this), event, onZoomStart);\n        };\n        this.onPinch = function (event) {\n            var disabled = _this.setup.disabled;\n            var _a = _this.props, onPinching = _a.onPinching, onZoom = _a.onZoom;\n            if (disabled)\n                return;\n            var isAllowed = isPinchAllowed(_this);\n            if (!isAllowed)\n                return;\n            event.preventDefault();\n            event.stopPropagation();\n            handlePinchZoom(_this, event);\n            handleCallback(getContext(_this), event, onPinching);\n            handleCallback(getContext(_this), event, onZoom);\n        };\n        this.onPinchStop = function (event) {\n            var _a = _this.props, onPinchingStop = _a.onPinchingStop, onZoomStop = _a.onZoomStop;\n            if (_this.pinchStartScale) {\n                handlePinchStop(_this);\n                handleCallback(getContext(_this), event, onPinchingStop);\n                handleCallback(getContext(_this), event, onZoomStop);\n            }\n        };\n        /// ///////\n        // Touch\n        /// ///////\n        this.onTouchPanningStart = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanningStart = _this.props.onPanningStart;\n            if (disabled)\n                return;\n            var isAllowed = isPanningStartAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            var isDoubleTap = _this.lastTouch &&\n                +new Date() - _this.lastTouch < 200 &&\n                event.touches.length === 1;\n            if (!isDoubleTap) {\n                _this.lastTouch = +new Date();\n                handleCancelAnimation(_this);\n                var touches = event.touches;\n                var isPanningAction = touches.length === 1;\n                var isPinchAction = touches.length === 2;\n                if (isPanningAction) {\n                    handleCancelAnimation(_this);\n                    handlePanningStart(_this, event);\n                    handleCallback(getContext(_this), event, onPanningStart);\n                }\n                if (isPinchAction) {\n                    _this.onPinchStart(event);\n                }\n            }\n        };\n        this.onTouchPanning = function (event) {\n            var disabled = _this.setup.disabled;\n            var onPanning = _this.props.onPanning;\n            if (_this.isPanning && event.touches.length === 1) {\n                if (disabled)\n                    return;\n                var isAllowed = isPanningAllowed(_this);\n                if (!isAllowed)\n                    return;\n                event.preventDefault();\n                event.stopPropagation();\n                var touch = event.touches[0];\n                handlePanning(_this, touch.clientX, touch.clientY);\n                handleCallback(getContext(_this), event, onPanning);\n            }\n            else if (event.touches.length > 1) {\n                _this.onPinch(event);\n            }\n        };\n        this.onTouchPanningStop = function (event) {\n            _this.onPanningStop(event);\n            _this.onPinchStop(event);\n        };\n        /// ///////\n        // Double Click\n        /// ///////\n        this.onDoubleClick = function (event) {\n            var disabled = _this.setup.disabled;\n            if (disabled)\n                return;\n            var isAllowed = isDoubleClickAllowed(_this, event);\n            if (!isAllowed)\n                return;\n            handleDoubleClick(_this, event);\n        };\n        /// ///////\n        // Helpers\n        /// ///////\n        this.clearPanning = function (event) {\n            if (_this.isPanning) {\n                _this.onPanningStop(event);\n            }\n        };\n        this.setKeyPressed = function (e) {\n            _this.pressedKeys[e.key] = true;\n        };\n        this.setKeyUnPressed = function (e) {\n            _this.pressedKeys[e.key] = false;\n        };\n        this.isPressingKeys = function (keys) {\n            if (!keys.length) {\n                return true;\n            }\n            return Boolean(keys.find(function (key) { return _this.pressedKeys[key]; }));\n        };\n        this.setTransformState = function (scale, positionX, positionY) {\n            var onTransformed = _this.props.onTransformed;\n            if (!Number.isNaN(scale) &&\n                !Number.isNaN(positionX) &&\n                !Number.isNaN(positionY)) {\n                if (scale !== _this.transformState.scale) {\n                    _this.transformState.previousScale = _this.transformState.scale;\n                    _this.transformState.scale = scale;\n                }\n                _this.transformState.positionX = positionX;\n                _this.transformState.positionY = positionY;\n                _this.applyTransformation();\n                var ctx_1 = getContext(_this);\n                _this.onChangeCallbacks.forEach(function (callback) { return callback(ctx_1); });\n                handleCallback(ctx_1, { scale: scale, positionX: positionX, positionY: positionY }, onTransformed);\n            }\n            else {\n                console.error(\"Detected NaN set state values\");\n            }\n        };\n        this.setCenter = function () {\n            if (_this.wrapperComponent && _this.contentComponent) {\n                var targetState = getCenterPosition(_this.transformState.scale, _this.wrapperComponent, _this.contentComponent);\n                _this.setTransformState(targetState.scale, targetState.positionX, targetState.positionY);\n            }\n        };\n        this.handleTransformStyles = function (x, y, scale) {\n            if (_this.props.customTransform) {\n                return _this.props.customTransform(x, y, scale);\n            }\n            return getTransformStyles(x, y, scale);\n        };\n        this.applyTransformation = function () {\n            if (!_this.mounted || !_this.contentComponent)\n                return;\n            var _a = _this.transformState, scale = _a.scale, positionX = _a.positionX, positionY = _a.positionY;\n            var transform = _this.handleTransformStyles(positionX, positionY, scale);\n            _this.contentComponent.style.transform = transform;\n        };\n        this.getContext = function () {\n            return getContext(_this);\n        };\n        /**\n         * Hooks\n         */\n        this.onChange = function (callback) {\n            if (!_this.onChangeCallbacks.has(callback)) {\n                _this.onChangeCallbacks.add(callback);\n            }\n            return function () {\n                _this.onChangeCallbacks.delete(callback);\n            };\n        };\n        this.onInit = function (callback) {\n            if (!_this.onInitCallbacks.has(callback)) {\n                _this.onInitCallbacks.add(callback);\n            }\n            return function () {\n                _this.onInitCallbacks.delete(callback);\n            };\n        };\n        /**\n         * Initialization\n         */\n        this.init = function (wrapperComponent, contentComponent) {\n            _this.cleanupWindowEvents();\n            _this.wrapperComponent = wrapperComponent;\n            _this.contentComponent = contentComponent;\n            handleCalculateBounds(_this, _this.transformState.scale);\n            _this.handleInitializeWrapperEvents(wrapperComponent);\n            _this.handleInitialize(wrapperComponent, contentComponent);\n            _this.initializeWindowEvents();\n            _this.isInitialized = true;\n            var ctx = getContext(_this);\n            handleCallback(ctx, undefined, _this.props.onInit);\n        };\n        this.props = props;\n        this.setup = createSetup(this.props);\n        this.transformState = createState(this.props);\n    }\n    return ZoomPanPinch;\n}());\n\nvar Context = react__WEBPACK_IMPORTED_MODULE_0___default().createContext(null);\nvar getContent = function (children, ctx) {\n    if (typeof children === \"function\") {\n        return children(ctx);\n    }\n    return children;\n};\nvar TransformWrapper = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function (props, ref) {\n    var instance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new ZoomPanPinch(props)).current;\n    var content = getContent(props.children, getControls(instance));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, function () { return getControls(instance); }, [instance]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        instance.update(props);\n    }, [instance, props]);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, { value: instance }, content);\n});\n\nvar KeepScale = react__WEBPACK_IMPORTED_MODULE_0___default().forwardRef(function (props, ref) {\n    var localRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var instance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return instance.onChange(function (ctx) {\n            if (localRef.current) {\n                var positionX = 0;\n                var positionY = 0;\n                localRef.current.style.transform = instance.handleTransformStyles(positionX, positionY, 1 / ctx.instance.transformState.scale);\n            }\n        });\n    }, [instance]);\n    return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, props, { ref: mergeRefs([localRef, ref]) }));\n});\n\nvar initialElementRect = {\n    width: 0,\n    height: 0,\n    y: 0,\n    x: 0,\n    top: 0,\n    bottom: 0,\n    left: 0,\n    right: 0,\n};\nvar useResize = function (ref, onResize, dependencies) {\n    var resizeObserverRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    var rectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(initialElementRect);\n    var didUnmount = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(function () {\n        var _a;\n        didUnmount.current = false;\n        if (!(\"ResizeObserver\" in window)) {\n            return;\n        }\n        if (ref) {\n            resizeObserverRef.current = new ResizeObserver(function (entries) {\n                var newSize = ref.getBoundingClientRect();\n                if (!Array.isArray(entries) ||\n                    !entries.length ||\n                    didUnmount.current ||\n                    (newSize.width === rectRef.current.width &&\n                        newSize.height === rectRef.current.height))\n                    return;\n                onResize(newSize, ref);\n                rectRef.current = newSize;\n            });\n            (_a = resizeObserverRef.current) === null || _a === void 0 ? void 0 : _a.observe(ref);\n        }\n        return function () {\n            var _a;\n            didUnmount.current = true;\n            if (ref) {\n                (_a = resizeObserverRef.current) === null || _a === void 0 ? void 0 : _a.unobserve(ref);\n            }\n        };\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, __spreadArray([onResize, ref], dependencies, true));\n};\n\nvar previewStyles = {\n    position: \"absolute\",\n    zIndex: 2,\n    top: \"0px\",\n    left: \"0px\",\n    boxSizing: \"border-box\",\n    border: \"3px solid red\",\n    transformOrigin: \"0% 0%\",\n    boxShadow: \"rgba(0,0,0,0.2) 0 0 0 10000000px\",\n};\nvar MiniMap = function (_a) {\n    var _b = _a.width, width = _b === void 0 ? 200 : _b, _c = _a.height, height = _c === void 0 ? 200 : _c, _d = _a.borderColor, borderColor = _d === void 0 ? \"red\" : _d, children = _a.children, rest = __rest(_a, [\"width\", \"height\", \"borderColor\", \"children\"]);\n    var _e = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false), initialized = _e[0], setInitialized = _e[1];\n    var instance = useTransformContext();\n    var miniMapInstance = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var mainRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var previewRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var getViewportSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (instance.wrapperComponent) {\n            var rect = instance.wrapperComponent.getBoundingClientRect();\n            return {\n                width: rect.width,\n                height: rect.height,\n            };\n        }\n        return {\n            width: 0,\n            height: 0,\n        };\n    }, [instance.wrapperComponent]);\n    var getContentSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        if (instance.contentComponent) {\n            var rect = instance.contentComponent.getBoundingClientRect();\n            return {\n                width: rect.width / instance.transformState.scale,\n                height: rect.height / instance.transformState.scale,\n            };\n        }\n        return {\n            width: 0,\n            height: 0,\n        };\n    }, [instance.contentComponent, instance.transformState.scale]);\n    var computeMiniMapScale = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n        var contentSize = getContentSize();\n        var scaleX = width / contentSize.width;\n        var scaleY = height / contentSize.height;\n        var scale = scaleY > scaleX ? scaleX : scaleY;\n        return scale;\n    }, [getContentSize, height, width]);\n    var computeMiniMapSize = function () {\n        var contentSize = getContentSize();\n        var scaleX = width / contentSize.width;\n        var scaleY = height / contentSize.height;\n        if (scaleY > scaleX) {\n            return { width: width, height: contentSize.height * scaleX };\n        }\n        return { width: contentSize.width * scaleY, height: height };\n    };\n    var computeMiniMapStyle = function () {\n        var scale = computeMiniMapScale();\n        var style = {\n            transform: \"scale(\".concat(scale || 1, \")\"),\n            transformOrigin: \"0% 0%\",\n            position: \"absolute\",\n            boxSizing: \"border-box\",\n            zIndex: 1,\n            overflow: \"hidden\",\n        };\n        Object.keys(style).forEach(function (key) {\n            if (wrapperRef.current) {\n                wrapperRef.current.style[key] = style[key];\n            }\n        });\n    };\n    var transformMiniMap = function () {\n        computeMiniMapStyle();\n        var miniSize = computeMiniMapSize();\n        var wrapSize = getContentSize();\n        if (wrapperRef.current) {\n            wrapperRef.current.style.width = \"\".concat(wrapSize.width, \"px\");\n            wrapperRef.current.style.height = \"\".concat(wrapSize.height, \"px\");\n        }\n        if (mainRef.current) {\n            mainRef.current.style.width = \"\".concat(miniSize.width, \"px\");\n            mainRef.current.style.height = \"\".concat(miniSize.height, \"px\");\n        }\n        if (previewRef.current) {\n            var size = getViewportSize();\n            var scale = computeMiniMapScale();\n            var previewScale = scale * (1 / instance.transformState.scale);\n            var transform = instance.handleTransformStyles(-instance.transformState.positionX * previewScale, -instance.transformState.positionY * previewScale, 1);\n            previewRef.current.style.transform = transform;\n            previewRef.current.style.width = \"\".concat(size.width * previewScale, \"px\");\n            previewRef.current.style.height = \"\".concat(size.height * previewScale, \"px\");\n        }\n    };\n    var initialize = function () {\n        transformMiniMap();\n    };\n    useTransformEffect(function () {\n        transformMiniMap();\n    });\n    useTransformInit(function () {\n        initialize();\n        setInitialized(true);\n    });\n    useResize(instance.contentComponent, initialize, [initialized]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        return instance.onChange(function (zpp) {\n            var scale = computeMiniMapScale();\n            if (miniMapInstance.current) {\n                miniMapInstance.current.instance.transformState.scale =\n                    zpp.instance.transformState.scale;\n                miniMapInstance.current.instance.transformState.positionX =\n                    zpp.instance.transformState.positionX * scale;\n                miniMapInstance.current.instance.transformState.positionY =\n                    zpp.instance.transformState.positionY * scale;\n            }\n        });\n    }, [computeMiniMapScale, instance, miniMapInstance]);\n    var wrapperStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function () {\n        return {\n            position: \"relative\",\n            zIndex: 2,\n            overflow: \"hidden\",\n        };\n    }, []);\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, rest, { ref: mainRef, style: wrapperStyle, className: \"rzpp-mini-map \".concat(rest.className || \"\") }),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, rest, { ref: wrapperRef, className: \"rzpp-wrapper\" }), children),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", { className: \"rzpp-preview\", ref: previewRef, style: __assign(__assign({}, previewStyles), { borderColor: borderColor }) })));\n};\n\nfunction styleInject(css, ref) {\n  if ( ref === void 0 ) ref = {};\n  var insertAt = ref.insertAt;\n\n  if (!css || typeof document === 'undefined') { return; }\n\n  var head = document.head || document.getElementsByTagName('head')[0];\n  var style = document.createElement('style');\n  style.type = 'text/css';\n\n  if (insertAt === 'top') {\n    if (head.firstChild) {\n      head.insertBefore(style, head.firstChild);\n    } else {\n      head.appendChild(style);\n    }\n  } else {\n    head.appendChild(style);\n  }\n\n  if (style.styleSheet) {\n    style.styleSheet.cssText = css;\n  } else {\n    style.appendChild(document.createTextNode(css));\n  }\n}\n\nvar css_248z = \".transform-component-module_wrapper__SPB86 {\\n  position: relative;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  overflow: hidden;\\n  -webkit-touch-callout: none; /* iOS Safari */\\n  -webkit-user-select: none; /* Safari */\\n  -khtml-user-select: none; /* Konqueror HTML */\\n  -moz-user-select: none; /* Firefox */\\n  -ms-user-select: none; /* Internet Explorer/Edge */\\n  user-select: none;\\n  margin: 0;\\n  padding: 0;\\n  transform: translate3d(0, 0, 0);\\n}\\n.transform-component-module_content__FBWxo {\\n  display: flex;\\n  flex-wrap: wrap;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n  height: -moz-fit-content;\\n  height: fit-content;\\n  margin: 0;\\n  padding: 0;\\n  transform-origin: 0% 0%;\\n}\\n.transform-component-module_content__FBWxo img {\\n  pointer-events: none;\\n}\\n\";\nvar styles = {\"wrapper\":\"transform-component-module_wrapper__SPB86\",\"content\":\"transform-component-module_content__FBWxo\"};\nstyleInject(css_248z);\n\nvar TransformComponent = function (_a) {\n    var children = _a.children, _b = _a.wrapperClass, wrapperClass = _b === void 0 ? \"\" : _b, _c = _a.contentClass, contentClass = _c === void 0 ? \"\" : _c, wrapperStyle = _a.wrapperStyle, contentStyle = _a.contentStyle, _d = _a.wrapperProps, wrapperProps = _d === void 0 ? {} : _d, _e = _a.contentProps, contentProps = _e === void 0 ? {} : _e;\n    var _f = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context), init = _f.init, cleanupWindowEvents = _f.cleanupWindowEvents;\n    var wrapperRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    var contentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var wrapper = wrapperRef.current;\n        var content = contentRef.current;\n        if (wrapper !== null && content !== null && init) {\n            init === null || init === void 0 ? void 0 : init(wrapper, content);\n        }\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        return function () {\n            cleanupWindowEvents === null || cleanupWindowEvents === void 0 ? void 0 : cleanupWindowEvents();\n        };\n    }, []);\n    return (react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, wrapperProps, { ref: wrapperRef, className: \"\".concat(baseClasses.wrapperClass, \" \").concat(styles.wrapper, \" \").concat(wrapperClass), style: wrapperStyle }),\n        react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", __assign({}, contentProps, { ref: contentRef, className: \"\".concat(baseClasses.contentClass, \" \").concat(styles.content, \" \").concat(contentClass), style: contentStyle }), children)));\n};\n\nvar useTransformContext = function () {\n    var libraryContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n    if (!libraryContext) {\n        throw new Error(\"Transform context must be placed inside TransformWrapper\");\n    }\n    return libraryContext;\n};\n\nvar useControls = function () {\n    var libraryContext = useTransformContext();\n    return getControls(libraryContext);\n};\n\nvar useTransformInit = function (callback) {\n    var libraryContext = useTransformContext();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var unmountCallback;\n        var unmount;\n        if (libraryContext.contentComponent && libraryContext.wrapperComponent) {\n            unmountCallback = callback(getState(libraryContext));\n        }\n        else {\n            unmount = libraryContext.onInit(function (ref) {\n                unmountCallback = callback(getState(ref.instance));\n            });\n        }\n        return function () {\n            unmount === null || unmount === void 0 ? void 0 : unmount();\n            unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();\n        };\n    }, []);\n};\n\nvar useTransformEffect = function (callback) {\n    var libraryContext = useTransformContext();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var unmountCallback;\n        var unmount = libraryContext.onChange(function (ref) {\n            unmountCallback = callback(getState(ref.instance));\n        });\n        return function () {\n            unmount();\n            unmountCallback === null || unmountCallback === void 0 ? void 0 : unmountCallback();\n        };\n    }, [callback, libraryContext]);\n};\n\nfunction useTransformComponent(callback) {\n    var libraryContext = useTransformContext();\n    var _a = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(callback(getState(libraryContext))), transformRender = _a[0], setTransformRender = _a[1];\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {\n        var mounted = true;\n        var unmount = libraryContext.onChange(function (ref) {\n            if (mounted) {\n                setTransformRender(callback(getState(ref.instance)));\n            }\n        });\n        return function () {\n            unmount();\n            mounted = false;\n        };\n    }, [callback, libraryContext]);\n    return transformRender;\n}\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-zoom-pan-pinch/dist/index.esm.js\n");

/***/ })

};
;