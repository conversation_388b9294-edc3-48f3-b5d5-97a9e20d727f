import re

def filter_log_file(log_path, filtered_log_path, threshold=100.0):
    """
    Reads a log file, removes lines where collimator data absolute values exceed a threshold,
    and also removes the preceding 'Thickness:' line for those entries.

    Args:
        log_path (str): The path to the input log file.
        filtered_log_path (str): The path to save the filtered log file.
        threshold (float): The value threshold for filtering.
    """
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
    except FileNotFoundError:
        print(f"错误：无法读取日志文件 {log_path}")
        return

    # A set to store the indices of lines to be removed
    indices_to_remove = set()

    # Regex to find the collimator data line and extract values
    collimator_regex = re.compile(
        r"点13均值x:([\d.-]+).*点13均值y:([\d.-]+).*点2x:([\d.-]+).*点2y:([\d.-]+)"
    )

    for i, line in enumerate(lines):
        match = collimator_regex.search(line)
        if match:
            values = [float(v) for v in match.groups()]
            # Check if any absolute value exceeds the threshold
            if any(abs(val) > threshold for val in values):
                # Mark this line and the previous line for removal
                indices_to_remove.add(i)
                if i > 0 and "Thickness:" in lines[i-1]:
                    indices_to_remove.add(i - 1)

    # Create the list of lines to keep
    filtered_lines = [line for i, line in enumerate(lines) if i not in indices_to_remove]

    # Write the filtered content to the new file
    with open(filtered_log_path, 'w', encoding='utf-8') as f:
        f.writelines(filtered_lines)

    print(f"处理完成。过滤后的日志已保存到 {filtered_log_path}")

if __name__ == "__main__":
    LOG_FILE_PATH = "v2.txt"
    FILTERED_LOG_FILE_PATH = "v2_filtered.txt"
    THRESHOLD = 100.0
    filter_log_file(LOG_FILE_PATH, FILTERED_LOG_FILE_PATH, THRESHOLD)