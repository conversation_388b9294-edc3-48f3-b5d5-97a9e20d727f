/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "(ssr)/./utils/snHelper.ts":
/*!***************************!*\
  !*** ./utils/snHelper.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findSN: () => (/* binding */ findSN),\n/* harmony export */   parseSnInput: () => (/* binding */ parseSnInput)\n/* harmony export */ });\n/**\r\n * Parses a string of SNs into a unique array of strings.\r\n * Supports comma, semicolon, and space as delimiters.\r\n * \r\n * @param input The raw string input from the user.\r\n * @returns A unique array of trimmed SNs.\r\n */ const parseSnInput = (input)=>{\n    if (!input || input.trim() === \"\") {\n        return [];\n    }\n    // Replace commas and semicolons with spaces to unify delimiters\n    const normalizedInput = input.replace(/[,;]/g, \" \");\n    // Split by one or more spaces, then trim and filter out empty strings\n    const sns = normalizedInput.split(/\\s+/).map((sn)=>sn.trim()).filter((sn)=>sn.length > 0);\n    // Return a unique set of SNs\n    return Array.from(new Set(sns));\n};\n/**\r\n * Finds the first SN from an \"insert into g_support\" SQL statement in a given text.\r\n * @param text The text to search within.\r\n * @returns The found SN or null if not found.\r\n */ const findSN = (text)=>{\n    const snRegex = /insert into g_support.*values\\s*\\(\"([^\"]+)\"/;\n    const match = snRegex.exec(text);\n    return match ? match[1] : null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/snHelper.ts\n");

/***/ }),

/***/ "(ssr)/./workers/dataExtractor.module.ts":
/*!*****************************************!*\
  !*** ./workers/dataExtractor.module.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractDataFromBlock: () => (/* binding */ extractDataFromBlock)\n/* harmony export */ });\n/* harmony import */ var _rulesEngine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rulesEngine */ \"(ssr)/./workers/rulesEngine.ts\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/snHelper */ \"(ssr)/./utils/snHelper.ts\");\n\n\n/**\r\n * Generates a descriptive name for a log block based on its start time and SN.\r\n * Format: yyyyMMdd_HHmmss\r\n * @param timestamp The timestamp string (e.g., \"2023-01-01 12:30:00,123\").\r\n * @returns A formatted block ID.\r\n */ const generateBlockId = (timestamp)=>{\n    if (!timestamp) {\n        return `NO_TIMESTAMP_${Date.now()}`;\n    }\n    const date = new Date(timestamp.replace(',', '.')); // Handle comma decimal separator\n    if (isNaN(date.getTime())) {\n        return `INVALID_TIMESTAMP_${Date.now()}`;\n    }\n    const y = date.getFullYear().toString().padStart(4, '0');\n    const m = (date.getMonth() + 1).toString().padStart(2, '0');\n    const d = date.getDate().toString().padStart(2, '0');\n    const h = date.getHours().toString().padStart(2, '0');\n    const min = date.getMinutes().toString().padStart(2, '0');\n    const s = date.getSeconds().toString().padStart(2, '0');\n    return `${y}${m}${d}_${h}_${min}_${s}`;\n};\n/**\r\n * Extracts all relevant data from a given block of log lines into a structured ProcessedBlock.\r\n * @param blockLines An array of strings, where each string is a line from the log.\r\n * @returns A ProcessedBlock object containing all extracted data.\r\n */ const extractDataFromBlock = (blockLines)=>{\n    const entireBlock = blockLines.join('\\n');\n    const collimationData = [];\n    const glueThicknessData = [];\n    const vacuumPumpEvents = [];\n    const ventValveEvents = [];\n    let startTime = null;\n    let endTime = null;\n    let lastSeenTimestamp = null;\n    const sn = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_1__.findSN)(entireBlock);\n    let version = 'UNKNOWN';\n    if (entireBlock.includes('####### 胶厚值:')) {\n        version = 'V1';\n    } else if (entireBlock.includes('Thickness:')) {\n        version = 'V2';\n    }\n    let inV2ChartDataSection = false;\n    for (const line of blockLines){\n        const timestampMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_TIMESTAMP);\n        if (timestampMatch && timestampMatch[1]) {\n            lastSeenTimestamp = timestampMatch[1];\n            if (!startTime) {\n                startTime = lastSeenTimestamp;\n            }\n            endTime = lastSeenTimestamp;\n        }\n        if (version === 'V2') {\n            if (line.includes('z轴停止完成')) {\n                inV2ChartDataSection = true;\n            }\n        }\n        if (version === 'V1') {\n            // --- V1 DATA EXTRACTION ---\n            const glueMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V1_GLUE_THICKNESS);\n            if (glueMatch && glueMatch[1] && lastSeenTimestamp) {\n                glueThicknessData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(glueMatch[1])\n                });\n            }\n            const collimationMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V1_COLLIMATION);\n            if (collimationMatch && collimationMatch[1] && lastSeenTimestamp) {\n                collimationData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(collimationMatch[1])\n                });\n            }\n        } else if (version === 'V2' && inV2ChartDataSection) {\n            // --- V2 DATA EXTRACTION (within the correct section) ---\n            const glueMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V2_GLUE_THICKNESS); // Use the imported regex\n            if (glueMatch && glueMatch[1] && lastSeenTimestamp) {\n                glueThicknessData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(glueMatch[1])\n                });\n            }\n            const v2CoordsMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V2_COORDS);\n            if (v2CoordsMatch && lastSeenTimestamp) {\n                try {\n                    const x1 = parseFloat(v2CoordsMatch[1]);\n                    const y1 = parseFloat(v2CoordsMatch[2]);\n                    const x2 = parseFloat(v2CoordsMatch[3]);\n                    const y2 = parseFloat(v2CoordsMatch[4]);\n                    if (x2 < 100000 && y2 < 100000) {\n                        const diff = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n                        if (Math.abs(diff) <= 100) {\n                            collimationData.push({\n                                timestamp: lastSeenTimestamp,\n                                value: diff\n                            });\n                        }\n                    }\n                } catch (e) {\n                    console.error(\"Error calculating V2 collimation diff:\", e);\n                }\n            }\n        }\n        if (version === 'V2') {\n            if (line.includes('轴已经停止')) {\n                inV2ChartDataSection = false;\n            }\n        }\n        if (line.includes('抽真空') && lastSeenTimestamp) {\n            vacuumPumpEvents.push({\n                timestamp: lastSeenTimestamp,\n                line_content: line,\n                type: 'vacuum'\n            });\n        }\n        if (line.includes('打开放气阀') && lastSeenTimestamp) {\n            ventValveEvents.push({\n                timestamp: lastSeenTimestamp,\n                line_content: line,\n                type: 'vent'\n            });\n        }\n    }\n    const blockId = generateBlockId(startTime);\n    const processedBlock = {\n        block_id: sn ? `${blockId}_${sn}` : blockId,\n        start_time: startTime,\n        end_time: endTime,\n        lines_count: blockLines.length,\n        vacuum_pump_events: vacuumPumpEvents,\n        vent_valve_events: ventValveEvents,\n        glue_thickness_values: glueThicknessData,\n        collimation_diff_values: collimationData,\n        sns: sn ? [\n            sn\n        ] : [],\n        raw_content: entireBlock,\n        version\n    };\n    return processedBlock;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./workers/dataExtractor.module.ts\n");

/***/ }),

/***/ "(ssr)/./workers/logParser.module.ts":
/*!*************************************!*\
  !*** ./workers/logParser.module.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findBlocksByTimestamp: () => (/* binding */ findBlocksByTimestamp),\n/* harmony export */   processLogFile: () => (/* binding */ processLogFile)\n/* harmony export */ });\n/* harmony import */ var _dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataExtractor.module */ \"(ssr)/./workers/dataExtractor.module.ts\");\n\n/**\r\n * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。\r\n * @param logContent 完整的日志内容字符串。\r\n * @returns 处理后的日志结果。\r\n */ function processLogFile(logContent) {\n    const lines = logContent.split(/\\r?\\n/);\n    const results = [];\n    let v1Count = 0;\n    let v2Count = 0;\n    let unknownCount = 0;\n    let endOfBlock = -1;\n    for(let i = lines.length - 1; i >= 0; i--){\n        // V1 and V2 blocks end with 'insert into g_support'\n        if (lines[i].includes('insert into g_support')) {\n            endOfBlock = i;\n            let startOfBlock = -1;\n            for(let j = endOfBlock; j >= 0; j--){\n                // V1 and V2 blocks start with '开始抽真空'\n                if (j < 10) {\n                    console.log(lines[j]);\n                }\n                if (lines[j].includes('开始抽真空')) {\n                    startOfBlock = j;\n                    break;\n                }\n            }\n            if (startOfBlock !== -1) {\n                const blockLines = lines.slice(startOfBlock, endOfBlock + 1);\n                const processedBlock = (0,_dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__.extractDataFromBlock)(blockLines);\n                results.push(processedBlock);\n                // Simple version detection for statistics\n                // This can be improved if more specific rules are available\n                if (processedBlock.version === 'V1') {\n                    v1Count++;\n                } else if (processedBlock.version === 'V2') {\n                    v2Count++;\n                } else {\n                    unknownCount++;\n                }\n                // Move the index to the beginning of the found block to avoid re-processing\n                i = startOfBlock;\n            }\n        }\n    }\n    // Since we are iterating backwards, reverse the results to restore chronological order\n    results.reverse();\n    return {\n        blocks: results,\n        totalLines: lines.length,\n        v1Count,\n        v2Count,\n        unknownCount\n    };\n}\n/**\r\n * Finds block IDs that match a given list of timestamps.\r\n * @param blocks The array of processed log blocks.\r\n * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.\r\n * @returns An array of block_ids that match the timestamps.\r\n */ function findBlocksByTimestamp(blocks, timestampsToMatch) {\n    const timestampSet = new Set(timestampsToMatch);\n    const matchedBlockIds = [];\n    for (const block of blocks){\n        if (block.start_time) {\n            try {\n                // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'\n                const date = new Date(block.start_time.replace(',', '.'));\n                const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);\n                if (timestampSet.has(blockTimestampInSeconds)) {\n                    matchedBlockIds.push(block.block_id);\n                }\n            } catch (e) {\n                console.error(`Error parsing date for block ${block.block_id}:`, e);\n            }\n        }\n    }\n    return matchedBlockIds;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./workers/logParser.module.ts\n");

/***/ }),

/***/ "(ssr)/./workers/logParser.worker.ts":
/*!*************************************!*\
  !*** ./workers/logParser.worker.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _logParser_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logParser.module */ \"(ssr)/./workers/logParser.module.ts\");\n\nlet processedLogBlocks = [];\n// 设置全局错误处理，以防有未捕获的异常\nself.addEventListener('error', (event)=>{\n    console.error('Unhandled error in worker:', event);\n    self.postMessage({\n        type: 'ERROR',\n        error: {\n            message: event.message,\n            filename: event.filename,\n            lineno: event.lineno\n        }\n    });\n});\n/**\r\n * 监听来自主线程的消息。\r\n */ self.onmessage = async (event)=>{\n    switch(event.data.type){\n        case 'PARSE_LOG':\n            {\n                const { logContent } = event.data.payload;\n                if (!logContent) {\n                    const errorMessage = {\n                        type: 'PARSE_LOG_RESULT',\n                        error: 'Invalid message format: logContent is missing in payload.'\n                    };\n                    console.error('[Worker] Posting error to main thread:', errorMessage);\n                    self.postMessage(errorMessage);\n                    return;\n                }\n                try {\n                    const processedData = await (0,_logParser_module__WEBPACK_IMPORTED_MODULE_0__.processLogFile)(logContent);\n                    processedLogBlocks = processedData.blocks; // Store processed blocks\n                    const message = {\n                        type: 'PARSE_LOG_RESULT',\n                        allBlocks: processedData.blocks\n                    };\n                    console.log('[Worker] Posting message to main thread:', message);\n                    self.postMessage(message);\n                } catch (error) {\n                    console.error('Error processing log file in worker:', error);\n                    const errorMessage = {\n                        type: 'PARSE_LOG_RESULT',\n                        error: error instanceof Error ? error.message : 'An unknown error occurred.'\n                    };\n                    console.error('[Worker] Posting error to main thread:', errorMessage);\n                    self.postMessage(errorMessage);\n                }\n                break;\n            }\n        case 'MATCH_BY_TIMESTAMP':\n            {\n                const { timestamps } = event.data.payload;\n                try {\n                    const matchedBlockIds = (0,_logParser_module__WEBPACK_IMPORTED_MODULE_0__.findBlocksByTimestamp)(processedLogBlocks, timestamps);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        payload: {\n                            matchedBlockIds\n                        }\n                    });\n                } catch (error) {\n                    console.error('Error during timestamp match in worker:', error);\n                    self.postMessage({\n                        type: 'MATCH_BY_TIMESTAMP_RESULT',\n                        error: 'An error occurred during the search.'\n                    });\n                }\n                break;\n            }\n        default:\n            console.error('Unknown message type:', event.data.type);\n            break;\n    }\n};\n// eslint-disable-next-line no-console\nconsole.log('Log parser worker initialized.');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./workers/logParser.worker.ts\n");

/***/ }),

/***/ "(ssr)/./workers/rulesEngine.ts":
/*!********************************!*\
  !*** ./workers/rulesEngine.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REGEX_TIMESTAMP: () => (/* binding */ REGEX_TIMESTAMP),\n/* harmony export */   REGEX_V1_COLLIMATION: () => (/* binding */ REGEX_V1_COLLIMATION),\n/* harmony export */   REGEX_V1_GLUE_THICKNESS: () => (/* binding */ REGEX_V1_GLUE_THICKNESS),\n/* harmony export */   REGEX_V2_COORDS: () => (/* binding */ REGEX_V2_COORDS),\n/* harmony export */   REGEX_V2_GLUE_THICKNESS: () => (/* binding */ REGEX_V2_GLUE_THICKNESS)\n/* harmony export */ });\n// hotel-dashboard/workers/rulesEngine.ts\nconst REGEX_TIMESTAMP = /(\\d{4}-\\d{2}-\\d{2}\\s\\d{2}:\\d{2}:\\d{2},\\d{3})/;\n// --- V1 Specific Rules ---\nconst REGEX_V1_GLUE_THICKNESS = /####### 胶厚值:([-\\d.]+)/;\nconst REGEX_V1_COLLIMATION = /####### 准直diff:([-\\d.]+)/;\n// --- V2 Specific Rules ---\nconst REGEX_V2_GLUE_THICKNESS = /^INFO.*Thickness:([-\\d.]+)/;\nconst REGEX_V2_COORDS = /点13均值x:([-\\d.]+),\\s*点13均值y:([-\\d.]+),\\s*点2x:([-\\d.]+),\\s*点2y:([-\\d.]+)/;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi93b3JrZXJzL3J1bGVzRW5naW5lLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEseUNBQXlDO0FBRWxDLE1BQU1BLGtCQUFrQiwrQ0FBK0M7QUFFOUUsNEJBQTRCO0FBQ3JCLE1BQU1DLDBCQUEwQix3QkFBd0I7QUFDeEQsTUFBTUMsdUJBQXVCLDJCQUEyQjtBQUUvRCw0QkFBNEI7QUFDckIsTUFBTUMsMEJBQTBCLDZCQUE2QjtBQUM3RCxNQUFNQyxrQkFBa0IseUVBQXlFIiwic291cmNlcyI6WyJEOlxccHljb2RlXFxzdXBwb3J0X2NoYXJ0MlxcaG90ZWwtZGFzaGJvYXJkXFx3b3JrZXJzXFxydWxlc0VuZ2luZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBob3RlbC1kYXNoYm9hcmQvd29ya2Vycy9ydWxlc0VuZ2luZS50c1xyXG5cclxuZXhwb3J0IGNvbnN0IFJFR0VYX1RJTUVTVEFNUCA9IC8oXFxkezR9LVxcZHsyfS1cXGR7Mn1cXHNcXGR7Mn06XFxkezJ9OlxcZHsyfSxcXGR7M30pLztcclxuXHJcbi8vIC0tLSBWMSBTcGVjaWZpYyBSdWxlcyAtLS1cclxuZXhwb3J0IGNvbnN0IFJFR0VYX1YxX0dMVUVfVEhJQ0tORVNTID0gLyMjIyMjIyMg6IO25Y6a5YC8OihbLVxcZC5dKykvO1xyXG5leHBvcnQgY29uc3QgUkVHRVhfVjFfQ09MTElNQVRJT04gPSAvIyMjIyMjIyDlh4bnm7RkaWZmOihbLVxcZC5dKykvO1xyXG5cclxuLy8gLS0tIFYyIFNwZWNpZmljIFJ1bGVzIC0tLVxyXG5leHBvcnQgY29uc3QgUkVHRVhfVjJfR0xVRV9USElDS05FU1MgPSAvXklORk8uKlRoaWNrbmVzczooWy1cXGQuXSspLztcclxuZXhwb3J0IGNvbnN0IFJFR0VYX1YyX0NPT1JEUyA9IC/ngrkxM+Wdh+WAvHg6KFstXFxkLl0rKSxcXHMq54K5MTPlnYflgLx5OihbLVxcZC5dKyksXFxzKueCuTJ4OihbLVxcZC5dKyksXFxzKueCuTJ5OihbLVxcZC5dKykvOyJdLCJuYW1lcyI6WyJSRUdFWF9USU1FU1RBTVAiLCJSRUdFWF9WMV9HTFVFX1RISUNLTkVTUyIsIlJFR0VYX1YxX0NPTExJTUFUSU9OIiwiUkVHRVhfVjJfR0xVRV9USElDS05FU1MiLCJSRUdFWF9WMl9DT09SRFMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./workers/rulesEngine.ts\n");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		var threw = true;
/******/ 		try {
/******/ 			__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 			threw = false;
/******/ 		} finally {
/******/ 			if(threw) delete __webpack_module_cache__[moduleId];
/******/ 		}
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("(ssr)/./workers/logParser.worker.ts");
/******/ 	module.exports = __webpack_exports__;
/******/ 	
/******/ })()
;