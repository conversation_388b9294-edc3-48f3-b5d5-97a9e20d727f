import { ProcessedBlock, TimestampedValue, SpecialEvent } from './logParser.definitions';
import {
    REGEX_TIMESTAMP,
    REGEX_V1_GLUE_THICKNESS,
    REGEX_V1_COLLIMATION,
    REGEX_V2_GLUE_THICKNESS,
    REGEX_V2_COORDS
} from './rulesEngine';
import { findSN } from '../utils/snHelper';

/**
 * Generates a descriptive name for a log block based on its start time and SN.
 * Format: yyyyMMdd_HHmmss
 * @param timestamp The timestamp string (e.g., "2023-01-01 12:30:00,123").
 * @returns A formatted block ID.
 */
const generateBlockId = (timestamp: string | null): string => {
    if (!timestamp) {
        return `NO_TIMESTAMP_${Date.now()}`;
    }
    const date = new Date(timestamp.replace(',', '.')); // Handle comma decimal separator
    if (isNaN(date.getTime())) {
        return `INVALID_TIMESTAMP_${Date.now()}`;
    }

    const y = date.getFullYear().toString().padStart(4, '0');
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    const s = date.getSeconds().toString().padStart(2, '0');

    return `${y}${m}${d}_${h}_${min}_${s}`;
};


/**
 * Extracts all relevant data from a given block of log lines into a structured ProcessedBlock.
 * @param blockLines An array of strings, where each string is a line from the log.
 * @returns A ProcessedBlock object containing all extracted data.
 */
export const extractDataFromBlock = (blockLines: string[]): ProcessedBlock => {
  const entireBlock = blockLines.join('\n');
  
  const collimationData: TimestampedValue[] = [];
  const glueThicknessData: TimestampedValue[] = [];
  const vacuumPumpEvents: SpecialEvent[] = [];
  const ventValveEvents: SpecialEvent[] = [];
  let startTime: string | null = null;
  let endTime: string | null = null;
  let lastSeenTimestamp: string | null = null;

  const sn = findSN(entireBlock);

  let version: 'V1' | 'V2' | 'UNKNOWN' = 'UNKNOWN';
  if (entireBlock.includes('####### 胶厚值:')) {
      version = 'V1';
  } else if (entireBlock.includes('ColimatorX13_preUV')) {
      version = 'V2';
  }

  console.log(`Version detected: ${version}`);
  console.log(`Block contains 'z轴停止完成': ${entireBlock.includes('z轴停止完成')}`);
  console.log(`Block contains '轴已经停止': ${entireBlock.includes('轴已经停止')}`);
  console.log(`Block contains 'Thickness:': ${entireBlock.includes('Thickness:')}`);
  console.log(`Block contains '点13均值x:': ${entireBlock.includes('点13均值x:')}`);
  console.log(`Block length: ${entireBlock.length} chars, ${blockLines.length} lines`);

  let inV2ChartDataSection = false;
  for (const line of blockLines) {
      const timestampMatch = line.match(REGEX_TIMESTAMP);
      if (timestampMatch && timestampMatch[1]) {
          lastSeenTimestamp = timestampMatch[1];
          if (!startTime) {
              startTime = lastSeenTimestamp;
          }
          endTime = lastSeenTimestamp;
      }

       if (version === 'V2') {
           if (line.includes('z轴停止完成')) {
               console.log(`V2: Entering chart data section at line: ${line}`);
               inV2ChartDataSection = true;
           }
       }

       if (version === 'V1') {
           // --- V1 DATA EXTRACTION ---
           const glueMatch = line.match(REGEX_V1_GLUE_THICKNESS);
           if (glueMatch && glueMatch[1] && lastSeenTimestamp) {
               glueThicknessData.push({ timestamp: lastSeenTimestamp, value: parseFloat(glueMatch[1]) });
           }

           const collimationMatch = line.match(REGEX_V1_COLLIMATION);
           if (collimationMatch && collimationMatch[1] && lastSeenTimestamp) {
               collimationData.push({ timestamp: lastSeenTimestamp, value: parseFloat(collimationMatch[1]) });
           }
       } else if (version === 'V2' && inV2ChartDataSection) {
           // --- V2 DATA EXTRACTION (within the correct section) ---
           const glueMatch = line.match(REGEX_V2_GLUE_THICKNESS); // Use the imported regex
           if (glueMatch && glueMatch[1] && lastSeenTimestamp) {
               console.log(`V2: Found glue thickness: ${glueMatch[1]} at ${lastSeenTimestamp}`);
               glueThicknessData.push({ timestamp: lastSeenTimestamp, value: parseFloat(glueMatch[1]) });
           }

           const v2CoordsMatch = line.match(REGEX_V2_COORDS);
           if (v2CoordsMatch && lastSeenTimestamp) {
               console.log(`V2: Found coords: x1=${v2CoordsMatch[1]}, y1=${v2CoordsMatch[2]}, x2=${v2CoordsMatch[3]}, y2=${v2CoordsMatch[4]}`);
               try {
                   const x1 = parseFloat(v2CoordsMatch[1]);
                   const y1 = parseFloat(v2CoordsMatch[2]);
                   const x2 = parseFloat(v2CoordsMatch[3]);
                   const y2 = parseFloat(v2CoordsMatch[4]);

                   if (x2 < 100000 && y2 < 100000) {
                       const diff = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
                       if (Math.abs(diff) <= 100) {
                          console.log(`V2: Calculated collimation diff: ${diff}`);
                          collimationData.push({ timestamp: lastSeenTimestamp, value: diff });
                       } else {
                          console.log(`V2: Collimation diff ${diff} exceeds threshold of 100`);
                       }
                   } else {
                       console.log(`V2: Coordinates out of range: x2=${x2}, y2=${y2}`);
                   }
               } catch (e) {
                   console.error("Error calculating V2 collimation diff:", e);
               }
           }
       }

       if (version === 'V2') {
           if (line.includes('轴已经停止')) {
               inV2ChartDataSection = false;
           }
       }

      if (line.includes('抽真空') && lastSeenTimestamp) {
          vacuumPumpEvents.push({ timestamp: lastSeenTimestamp, line_content: line, type: 'vacuum' });
      }
      if (line.includes('打开放气阀') && lastSeenTimestamp) {
          ventValveEvents.push({ timestamp: lastSeenTimestamp, line_content: line, type: 'vent' });
      }
  }

  const blockId = generateBlockId(startTime);

  const processedBlock: ProcessedBlock = {
    block_id: sn ? `${blockId}_${sn}` : blockId,
    start_time: startTime,
    end_time: endTime,
    lines_count: blockLines.length,
    vacuum_pump_events: vacuumPumpEvents,
    vent_valve_events: ventValveEvents,
    glue_thickness_values: glueThicknessData,
    collimation_diff_values: collimationData,
    sns: sn ? [sn] : [],
    raw_content: entireBlock,
    version,
  };

  return processedBlock;
};