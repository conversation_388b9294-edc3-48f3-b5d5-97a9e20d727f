import { ProcessedBlock, TimestampedValue, SpecialEvent } from './logParser.definitions';
import {
    REGEX_TIMESTAMP,
    REGEX_V1_GLUE_THICKNESS,
    REGEX_V1_COLLIMATION,
    REGEX_V2_GLUE_THICKNESS,
    REGEX_V2_COORDS,
    REGEX_INSERT_AFTER_UV_DIFF
} from './rulesEngine';
import { findSN } from '../utils/snHelper';

/**
 * 根据时间戳生成一个标准化的、用作唯一标识符的块 ID。
 *
 * 此函数接收一个可能的时间戳字符串，并将其转换为 `YYYYMMDD_HH_mm_ss` 格式。
 * 这种格式化的 ID 易于人类阅读，并且可以按时间顺序排序。
 *
 * - **容错处理**:
 *   - 如果时间戳为 `null`，则生成一个基于当前时间的备用 ID，格式为 `NO_TIMESTAMP_...`。
 *   - 如果时间戳字符串无法被解析为有效日期（例如，格式错误），则生成一个 `INVALID_TIMESTAMP_...` 备用 ID。
 *   - 它能处理时间戳中小数点为逗号（','）的情况，通过替换为点（'.'）来提高解析的健壮性。
 *
 * @param timestamp - 块的起始时间戳字符串 (例如, "2023-01-01 12:30:00,123")。如果块没有起始时间，则为 `null`。
 * @returns 返回格式为 `YYYYMMDD_HH_mm_ss` 的字符串，或在时间戳无效时返回一个备用 ID。
 */
const generateBlockId = (timestamp: string | null): string => {
    if (!timestamp) {
        return `NO_TIMESTAMP_${Date.now()}`;
    }
    const date = new Date(timestamp.replace(',', '.')); // Handle comma decimal separator
    if (isNaN(date.getTime())) {
        return `INVALID_TIMESTAMP_${Date.now()}`;
    }

    const y = date.getFullYear().toString().padStart(4, '0');
    const m = (date.getMonth() + 1).toString().padStart(2, '0');
    const d = date.getDate().toString().padStart(2, '0');
    const h = date.getHours().toString().padStart(2, '0');
    const min = date.getMinutes().toString().padStart(2, '0');
    const s = date.getSeconds().toString().padStart(2, '0');

    return `${y}${m}${d}_${h}_${min}_${s}`;
};


/**
 * 从给定的日志行块中提取所有相关数据，并将其构造成一个 `ProcessedBlock` 对象。
 *
 * 该函数负责以下核心任务：
 * 1.  **版本检测**: 通过在整个块中搜索特定关键字（如 '####### 胶厚值:' 或 'Thickness:'）来自动识别日志是 V1 还是 V2 格式。
 * 2.  **数据提取**:
 *     - **时间戳**: 逐行解析时间戳，并维护一个 `lastSeenTimestamp` 状态，以便为没有时间戳的数据行关联正确的时间。同时确定块的 `startTime` 和 `endTime`。
 *     - **SN**: 调用 `findSN` 辅助函数从 `insert into` 语句中提取序列号。
 *     - **V1 数据**: 如果检测为 V1，则使用 V1 的正则表达式提取准直和胶厚数据。
 *     - **V2 数据**: 如果检测为 V2，则首先定位到一个由特定标记（如“z轴停止完成”和“轴已经停止”）界定的“图表数据区域”。然后在此区域内，使用 V2 的正则表达式提取胶厚和坐标数据，并根据坐标计算准直差值。
 *     - **异常值过滤**: 在处理 V2 准直数据时，会进行异常值过滤，只保留有效的计算结果。
 * 3.  **特殊事件**: 识别并记录关键事件，如“抽真空”和“打开放气阀”。
 * 4.  **对象构建**: 将所有提取的数据（包括元数据、数据点、事件和原始文本）组装成一个结构化的 `ProcessedBlock` 对象，并为其生成一个唯一的 `block_id`。
 *
 * @param blockLines - 一个字符串数组，其中每个字符串是日志文件中的一行。
 * @returns 一个 `ProcessedBlock` 对象，包含了从日志块中提取的所有结构化信息。
 */
export const extractDataFromBlock = (blockLines: string[]): ProcessedBlock => {
  const entireBlock = blockLines.join('\n');
  
  const collimationData: TimestampedValue[] = [];
  const glueThicknessData: TimestampedValue[] = [];
  const vacuumPumpEvents: SpecialEvent[] = [];
  const ventValveEvents: SpecialEvent[] = [];
  let startTime: string | null = null;
  let endTime: string | null = null;
  let lastSeenTimestamp: string | null = null;
  let preVentCollimatorDiff: number | null = null;
  let collimatorDiffAfterUV: number | null = null;

  const sn = findSN(entireBlock);

  let version: 'V1' | 'V2' | 'UNKNOWN' = 'UNKNOWN';

  // V1版本检测 - 查找"胶厚值"标记
  if (entireBlock.includes('####### 胶厚值:') ||
      entireBlock.includes('####### 鑳跺帤鍊�:')) {
      version = 'V1';
  }
  // V2版本检测 - 查找"Thickness:"标记或其他V2特有标记
  else if (entireBlock.includes('Thickness:') ||
           entireBlock.includes('点13均值x:') ||
           entireBlock.includes('鐐�13鍧囧€紉:')) {
      version = 'V2';
  }

  console.log(`版本检测结果: ${version}`);

  let inV2ChartDataSection = false;
  for (const line of blockLines) {
       const timestampMatch = line.match(REGEX_TIMESTAMP);
       if (timestampMatch && timestampMatch[1]) {
           lastSeenTimestamp = timestampMatch[1];
           if (!startTime) {
               startTime = lastSeenTimestamp;
           }
           endTime = lastSeenTimestamp;
       }

       // Extract ColimatorDiff_afterUV from the insert statement
       if (line.includes('insert into')) {
           const match = line.match(REGEX_INSERT_AFTER_UV_DIFF);
           if (match && match[1] && match[2]) {
               const columns = match[1].split(',').map(s => s.trim());
               const values = match[2].split(',').map(s => s.trim());
               const columnIndex = columns.indexOf('ColimatorDiff_afterUV');
               if (columnIndex !== -1 && values[columnIndex]) {
                   // Remove potential quotes from the value before parsing
                   const cleanedValue = values[columnIndex].replace(/"/g, '');
                   collimatorDiffAfterUV = parseFloat(cleanedValue);
               }
           }
       }

       if (version === 'V2') {
           // 处理多种编码的"z轴停止完成"
           if (line.includes('z轴停止完成') ||
               line.includes('z杞村仠姝㈠畬鎴�') ||
               line.includes('\u007a\u8f74\u505c\u6b62\u5b8c\u6210') ||
               /z.*轴.*停.*止.*完.*成/.test(line)) {
               console.log(`V2: 进入数据收集区间: ${line}`);
               inV2ChartDataSection = true;
           }
       }

       if (version === 'V1') {
           // --- V1 DATA EXTRACTION ---
           const glueMatch = line.match(REGEX_V1_GLUE_THICKNESS);
           if (glueMatch && glueMatch[1] && lastSeenTimestamp) {
               glueThicknessData.push({ timestamp: lastSeenTimestamp, value: parseFloat(glueMatch[1]) });
           }

           const collimationMatch = line.match(REGEX_V1_COLLIMATION);
           if (collimationMatch && collimationMatch[1] && lastSeenTimestamp) {
               collimationData.push({ timestamp: lastSeenTimestamp, value: parseFloat(collimationMatch[1]) });
           }
       } else if (version === 'V2' && inV2ChartDataSection) {
           // --- V2 DATA EXTRACTION (within the correct section) ---
           const glueMatch = line.match(REGEX_V2_GLUE_THICKNESS); // Use the imported regex
           if (glueMatch && glueMatch[1] && lastSeenTimestamp) {
               glueThicknessData.push({ timestamp: lastSeenTimestamp, value: parseFloat(glueMatch[1]) });
           }

           const v2CoordsMatch = line.match(REGEX_V2_COORDS);
           if (v2CoordsMatch && lastSeenTimestamp) {
               try {
                   const x1 = parseFloat(v2CoordsMatch[1]);
                   const y1 = parseFloat(v2CoordsMatch[2]);
                   const x2 = parseFloat(v2CoordsMatch[3]);
                   const y2 = parseFloat(v2CoordsMatch[4]);
                   
                   if (x2 < 100000 && y2 < 100000) {
                       const diff = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));
                       if (Math.abs(diff) <= 1) {
                          collimationData.push({ timestamp: lastSeenTimestamp, value: diff });
                       }
                   }
               } catch (e) {
                   console.error("Error calculating V2 collimation diff:", e);
               }
           }
       }

       if (version === 'V2') {
           // 处理多种编码的"轴已经停止"
           if (line.includes('轴已经停止') ||
               line.includes('杞村凡缁忓仠姝�') ||
               line.includes('\u8f74\u5df2\u7ecf\u505c\u6b62') ||
               /轴.*已.*经.*停.*止/.test(line)) {
               console.log(`V2: 退出数据收集区间: ${line}`);
               inV2ChartDataSection = false;
           }
       }

      if (line.includes('抽真空') && lastSeenTimestamp) {
          vacuumPumpEvents.push({ timestamp: lastSeenTimestamp, line_content: line, type: 'vacuum' });
      }
      if (line.includes('打开放气阀') && lastSeenTimestamp) {
          ventValveEvents.push({ timestamp: lastSeenTimestamp, line_content: line, type: 'vent' });
      }
  }

  // Find the pre-vent collimator diff
  if (ventValveEvents.length > 0) {
      const lastVentEvent = ventValveEvents[ventValveEvents.length - 1];
      const ventTimestamp = lastVentEvent.timestamp;

      let latestCollimationBeforeVent: TimestampedValue | null = null;

      for (const collimation of collimationData) {
          if (collimation.timestamp < ventTimestamp) {
              if (!latestCollimationBeforeVent || collimation.timestamp > latestCollimationBeforeVent.timestamp) {
                  latestCollimationBeforeVent = collimation;
              }
          }
      }

      if (latestCollimationBeforeVent) {
          preVentCollimatorDiff = latestCollimationBeforeVent.value;
      }
  }
 
  const blockId = generateBlockId(startTime);
 
  const processedBlock: ProcessedBlock = {
    block_id: sn ? `${blockId}_${sn}` : blockId,
    start_time: startTime,
    end_time: endTime,
    lines_count: blockLines.length,
    vacuum_pump_events: vacuumPumpEvents,
    vent_valve_events: ventValveEvents,
    glue_thickness_values: glueThicknessData,
    collimation_diff_values: collimationData,
    sns: sn ? [sn] : [],
    raw_content: entireBlock,
    version,
    preVentCollimatorDiff,
    collimatorDiffAfterUV,
  };
 
  return processedBlock;
};