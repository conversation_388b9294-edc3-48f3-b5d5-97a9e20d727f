const fs = require('fs');
const path = require('path');

// Import the parsing functions - need to compile TypeScript first
const { processLogFile } = require('./hotel-dashboard/dist/workers/logParser.module.js');

// Read the v2.txt file
const v2FilePath = path.join(__dirname, 'v2.txt');
const v2Content = fs.readFileSync(v2FilePath, 'utf8');

console.log('=== Testing v2.txt parsing ===');
console.log(`File size: ${v2Content.length} characters`);
console.log(`Lines count: ${v2Content.split('\n').length}`);

// Process the log file
const results = processLogFile(v2Content);

console.log('\n=== Parsing Results ===');
console.log(`Total blocks found: ${results.blocks.length}`);
console.log(`V1 blocks: ${results.v1Count}`);
console.log(`V2 blocks: ${results.v2Count}`);
console.log(`Unknown blocks: ${results.unknownCount}`);

// Show details of each block
results.blocks.forEach((block, index) => {
    console.log(`\n--- Block ${index + 1} ---`);
    console.log(`Block ID: ${block.block_id}`);
    console.log(`Version: ${block.version}`);
    console.log(`Start time: ${block.start_time}`);
    console.log(`End time: ${block.end_time}`);
    console.log(`Lines count: ${block.lines_count}`);
    console.log(`Glue thickness values: ${block.glue_thickness_values.length}`);
    console.log(`Collimation diff values: ${block.collimation_diff_values.length}`);
    console.log(`SNs: ${JSON.stringify(block.sns)}`);
    
    // Show first few data points if available
    if (block.glue_thickness_values.length > 0) {
        console.log(`First glue thickness: ${block.glue_thickness_values[0].value} at ${block.glue_thickness_values[0].timestamp}`);
    }
    if (block.collimation_diff_values.length > 0) {
        console.log(`First collimation diff: ${block.collimation_diff_values[0].value} at ${block.collimation_diff_values[0].timestamp}`);
    }
});
