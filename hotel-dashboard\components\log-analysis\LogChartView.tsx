"use client";

import React, { useMemo, useState, useRef, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  <PERSON>Axis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
  ComposedChart
} from 'recharts'; 
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

interface LogChartViewProps {
  dataChunks: ProcessedBlock[];
  selectedBlockIds: string[];
  onBlockSelect: (blockId: string) => void;
  isHighlighted?: boolean;
  chartRefs?: React.MutableRefObject<Map<string, HTMLDivElement | null>>;
  onRenderComplete?: () => void;
}

interface ChartDataPoint {
  time: number; 
  glueThickness: number | null;
  collimationDiff: number | null;
}

interface EventPoint { 
  time: number;
  value: number;
  label: string;
}

// Helper functions (parseTimestamp, processBlockData) defined at the top-level
// so they are accessible by both LogChartView and ChartRenderer.
const parseTimestamp = (timestamp: string | null | undefined): number => {
  if (!timestamp) return NaN;
  try {
    let standardizedTimestamp = timestamp.replace(",", ".");
    if (!standardizedTimestamp.includes("T") && standardizedTimestamp.includes(" ")) {
      const parts = standardizedTimestamp.split(" ");
      if (parts.length > 1 && parts[0].includes("-") && parts[1].includes(":")) {
        standardizedTimestamp = parts[0] + "T" + parts.slice(1).join(" ");
      }
    }
    let date = new Date(standardizedTimestamp);
    let time = date.getTime();
    if (isNaN(time)) {
      const slightlyLessStandardized = timestamp.replace(",", ".");
      date = new Date(slightlyLessStandardized);
      time = date.getTime();
    }
    if (isNaN(time)) {
      date = new Date(timestamp);
      time = date.getTime();
    }
    if (isNaN(time)) {
      console.warn(`[LogChartViewHelper] Failed to parse timestamp: "${timestamp}"`);
      return NaN;
    }
    return time;
  } catch (error) {
    console.error(`[LogChartViewHelper] Error parsing timestamp: "${timestamp}"`, error);
    return NaN;
  }
};

const processBlockData = (block: ProcessedBlock): ChartDataPoint[] => {
  const dataPoints: ChartDataPoint[] = [];
  (block.glue_thickness_values || []).forEach(value => {
    const time = parseTimestamp(value.timestamp);
    if (!isNaN(time)) dataPoints.push({ time, glueThickness: value.value, collimationDiff: null });
  });
  (block.collimation_diff_values || []).forEach(value => {
    const time = parseTimestamp(value.timestamp);
    if (!isNaN(time)) dataPoints.push({ time, glueThickness: null, collimationDiff: value.value });
  });
  dataPoints.sort((a, b) => a.time - b.time);
  const mergedPoints: ChartDataPoint[] = [];
  dataPoints.forEach(point => {
    const existingPoint = mergedPoints.find(p => p.time === point.time);
    if (existingPoint) {
      if (point.glueThickness !== null) existingPoint.glueThickness = point.glueThickness;
      if (point.collimationDiff !== null) existingPoint.collimationDiff = point.collimationDiff;
    } else {
      mergedPoints.push({ ...point });
    }
  });
  return mergedPoints;
};

interface ChartRendererProps {
  chunk: ProcessedBlock;
  isChartReady: boolean;
}

const ChartRenderer: React.FC<ChartRendererProps> = ({ chunk, isChartReady }) => {
  const {
    chartDataForThisBlock,
    eventPointsForThisBlock,
    timeDomainForThisBlock,
    glueDomainForThisBlock,
    collimationDomainForThisBlock,
    hasDataForThisBlock
  } = useMemo(() => {
    if (!chunk) {
      return {
        chartDataForThisBlock: [], eventPointsForThisBlock: [],
        timeDomainForThisBlock: [Date.now() - 3600000, Date.now()] as [number, number],
        glueDomainForThisBlock: [0, 1000] as [number, number], collimationDomainForThisBlock: [0, 0.1] as [number, number],
        hasDataForThisBlock: false,
      };
    }
    const processedDataPoints = processBlockData(chunk);
    const vacuumEvents = (chunk.vacuum_pump_events || []).map(event => ({
      time: parseTimestamp(event.timestamp),
      label: `抽真空 (${event.timestamp.split(' ')[1] || event.timestamp})`,
      color: 'rgba(128,128,128,0.7)' // Grey color for vacuum
    }));

    const ventEvents = (chunk.vent_valve_events || []).map(event => ({
        time: parseTimestamp(event.timestamp),
        label: `打开放气阀 (${event.timestamp.split(' ')[1] || event.timestamp})`,
        color: 'rgba(255,0,0,0.7)' // Red color for vent
    }));

    const currentEventPoints = [...vacuumEvents, ...ventEvents]
        .filter(ep => !isNaN(ep.time))
        .map(ep => ({ ...ep, value: 0 })); // Add value property after filtering
    currentEventPoints.sort((a, b) => a.time - b.time);

    let timeDom: [number, number] = [Date.now() - 3600000, Date.now()];
    if (processedDataPoints.length > 0) {
        const times = processedDataPoints.map(p => p.time).filter(t => !isNaN(t));
        if (times.length > 0) {
            timeDom = [Math.min(...times), Math.max(...times)];
        }
    }
    if (timeDom[0] === timeDom[1]) timeDom[1] = timeDom[0] + 3600000;


    const glueValues = processedDataPoints.map(p => p.glueThickness).filter((v): v is number => v !== null && !isNaN(v));
    let glueDom: [number, number] = [0, 1000];
    if (glueValues.length > 0) glueDom = [Math.min(...glueValues), Math.max(...glueValues)];
    if (glueDom[0] === glueDom[1]) glueDom[1] = glueDom[0] + 10;
    
    const collimValues = processedDataPoints.map(p => p.collimationDiff).filter((v): v is number => v !== null && !isNaN(v));
    let collimDom: [number, number] = [0, 0.1];
    if (collimValues.length > 0) collimDom = [Math.min(...collimValues), Math.max(...collimValues)];
    if (collimDom[0] === collimDom[1]) collimDom[1] = collimDom[0] + 0.01;

    return {
      chartDataForThisBlock: processedDataPoints, eventPointsForThisBlock: currentEventPoints,
      timeDomainForThisBlock: timeDom, glueDomainForThisBlock: glueDom,
      collimationDomainForThisBlock: collimDom, hasDataForThisBlock: processedDataPoints.length > 0,
    };
  }, [chunk]);

  const shouldRenderChartContent = hasDataForThisBlock && isChartReady;

  if (!shouldRenderChartContent) {
    return (
      <div className="flex items-center justify-center h-full text-muted-foreground">
        {!isChartReady ? "图表加载中..." : "此数据块无有效图表数据。"}
      </div>
    );
  }

  return (
    <ResponsiveContainer width="100%" height="100%">
      <ComposedChart data={chartDataForThisBlock} margin={{ top: 20, right: 40, left: 30, bottom: 20 }}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="time" domain={timeDomainForThisBlock} type="number" tickFormatter={(value) => new Date(value).toLocaleTimeString()} allowDuplicatedCategory={false} />
        <YAxis yAxisId="glue" orientation="left" domain={glueDomainForThisBlock} type="number" stroke="#8884d8" label={{ value: '胶厚', angle: -90, position: 'insideLeft', offset: -5, style: { fill: '#8884d8', textAnchor: 'middle' } }} tickFormatter={(value) => value.toFixed(2)} width={70} />
        <YAxis yAxisId="collimation" orientation="right" domain={collimationDomainForThisBlock} type="number" stroke="#82ca9d" label={{ value: '准直差', angle: 90, position: 'insideRight', offset: -15, style: { fill: '#82ca9d', textAnchor: 'middle' } }} tickFormatter={(value) => value.toFixed(3)} width={80} />
        <Tooltip labelFormatter={(value) => new Date(value).toLocaleString()} formatter={(value: any, name: string) => { if (typeof value !== 'number') return [value, name]; if (name === 'glueThickness') return [value.toFixed(2), '胶厚']; if (name === 'collimationDiff') return [value.toFixed(3), '准直差']; return [value, name]; }} contentStyle={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }} />
        <Legend verticalAlign="top" height={36} wrapperStyle={{ paddingBottom: '10px' }} />
        <Line yAxisId="glue" type="monotone" dataKey="glueThickness" name="胶厚" stroke="#8884d8" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 5 }} isAnimationActive={false} connectNulls={true} />
        <Line yAxisId="collimation" type="monotone" dataKey="collimationDiff" name="准直差" stroke="#82ca9d" strokeWidth={2} dot={{ r: 2 }} activeDot={{ r: 5 }} isAnimationActive={false} connectNulls={true} />
        {eventPointsForThisBlock.map((event, index) => (
          <ReferenceLine key={`event-${chunk.block_id}-${index}`} x={event.time} stroke={event.color} yAxisId="glue" strokeDasharray="4 4" label={{ value: event.label, position: 'insideTopRight', fill: event.color, fontSize: 10 }} />
        ))}
      </ComposedChart>
    </ResponsiveContainer>
  );
};

export const LogChartView = React.forwardRef<HTMLDivElement, LogChartViewProps>(
  ({ dataChunks, selectedBlockIds, onBlockSelect, isHighlighted = false, chartRefs, onRenderComplete }, ref) => {
    const [isChartReady, setIsChartReady] = useState(false);
    const { toast } = useToast();

    useEffect(() => {
      setIsChartReady(false);
      const timer = setTimeout(() => {
        setIsChartReady(true);
        // After setting state and allowing a tick for the render to be scheduled,
        // call the onRenderComplete callback if it exists.
        if (onRenderComplete) {
          // A minimal additional delay to give Recharts a moment to draw.
          setTimeout(onRenderComplete, 50);
        }
      }, 100); // Base delay to get past initial render.
      return () => clearTimeout(timer);
    }, [selectedBlockIds, dataChunks, onRenderComplete]);

    const hasChartData = useMemo(() => {
      if (!dataChunks || !Array.isArray(dataChunks)) {
        return false;
      }
      return dataChunks.length > 0;
    }, [dataChunks]);

    return (
      <div ref={ref} className="space-y-6 log-chart-container">
        {dataChunks.map((chunk) => (
          <Card
            key={chunk.block_id}
            ref={(el) => {
              if (chartRefs) {
                chartRefs.current.set(chunk.block_id, el);
              }
            }}
            data-block-id={chunk.block_id}
            className={`${selectedBlockIds.includes(chunk.block_id) ? 'block' : 'hidden'} ${isHighlighted ? 'ring-2 ring-offset-2 ring-blue-500' : ''}`}
            style={{
              display: selectedBlockIds.includes(chunk.block_id) ? 'block' : 'none',
              minHeight: selectedBlockIds.includes(chunk.block_id) ? '450px' : '0'
            }}
          >
            <CardHeader className="flex flex-row justify-between items-center">
              <CardTitle>数据块 {chunk.block_id}</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="w-full h-[400px] min-h-[400px]" style={{ width: '100%', height: '400px' }}>
                <ChartRenderer chunk={chunk} isChartReady={isChartReady} />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }
);

LogChartView.displayName = 'LogChartView';

export default LogChartView;