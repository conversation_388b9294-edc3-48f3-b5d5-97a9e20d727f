"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/logParser.module.ts":
/*!*************************************!*\
  !*** ./workers/logParser.module.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   findBlocksByTimestamp: () => (/* binding */ findBlocksByTimestamp),\n/* harmony export */   processLogFile: () => (/* binding */ processLogFile)\n/* harmony export */ });\n/* harmony import */ var _dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dataExtractor.module */ \"(app-pages-browser)/./workers/dataExtractor.module.ts\");\n\n/**\r\n * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。\r\n * @param logContent 完整的日志内容字符串。\r\n * @returns 处理后的日志结果。\r\n */ function processLogFile(logContent) {\n    var _lines_, _lines_1;\n    const lines = logContent.split(/\\r?\\n/);\n    console.log(\"Total lines in file: \".concat(lines.length));\n    console.log('First line: \"'.concat(lines[0], '\" (length: ').concat((_lines_ = lines[0]) === null || _lines_ === void 0 ? void 0 : _lines_.length, \")\"));\n    console.log('Last line: \"'.concat(lines[lines.length - 1], '\" (length: ').concat((_lines_1 = lines[lines.length - 1]) === null || _lines_1 === void 0 ? void 0 : _lines_1.length, \")\"));\n    const results = [];\n    let v1Count = 0;\n    let v2Count = 0;\n    let unknownCount = 0;\n    let endOfBlock = -1;\n    for(let i = lines.length - 1; i >= 0; i--){\n        // V1 and V2 blocks end with 'insert into g_support'\n        if (lines[i].includes('insert into g_support')) {\n            console.log(\"Found block end at array index \".concat(i, \" (line \").concat(i + 1, \"): \").concat(lines[i].substring(0, 100), \"...\"));\n            endOfBlock = i;\n            let startOfBlock = -1;\n            for(let j = endOfBlock; j >= 0; j--){\n                // V1 and V2 blocks start with '开始抽真空'\n                if (j === 0) {\n                    console.log('Checking first line (index 0): \"'.concat(lines[j], '\" (length: ').concat(lines[j].length, \")\"));\n                    console.log(\"Contains '开始抽真空': \".concat(lines[j].includes('开始抽真空')));\n                    console.log(\"Line bytes: \".concat(Array.from(lines[j]).map((c)=>c.charCodeAt(0)).join(', ')));\n                }\n                // Try both exact match and handle encoding issues\n                const line = lines[j].trim();\n                // Check for both correct encoding and potential encoding issues\n                if (line.includes('开始抽真空') || line.includes('寮€濮嬫娊鐪熺┖')) {\n                    console.log(\"Found block start at line \".concat(j, \": \").concat(lines[j]));\n                    startOfBlock = j;\n                    break;\n                }\n            }\n            console.log(\"Search completed. startOfBlock = \".concat(startOfBlock, \", endOfBlock = \").concat(endOfBlock));\n            if (startOfBlock !== -1) {\n                console.log(\"Processing block from line \".concat(startOfBlock, \" to \").concat(endOfBlock, \" (\").concat(endOfBlock - startOfBlock + 1, \" lines)\"));\n                const blockLines = lines.slice(startOfBlock, endOfBlock + 1);\n                const processedBlock = (0,_dataExtractor_module__WEBPACK_IMPORTED_MODULE_0__.extractDataFromBlock)(blockLines);\n                console.log(\"Processed block: version=\".concat(processedBlock.version, \", glue_thickness=\").concat(processedBlock.glue_thickness_values.length, \", collimation=\").concat(processedBlock.collimation_diff_values.length));\n                results.push(processedBlock);\n                // Simple version detection for statistics\n                // This can be improved if more specific rules are available\n                if (processedBlock.version === 'V1') {\n                    v1Count++;\n                } else if (processedBlock.version === 'V2') {\n                    v2Count++;\n                } else {\n                    unknownCount++;\n                }\n                // Move the index to the beginning of the found block to avoid re-processing\n                i = startOfBlock;\n            }\n        }\n    }\n    // Since we are iterating backwards, reverse the results to restore chronological order\n    results.reverse();\n    return {\n        blocks: results,\n        totalLines: lines.length,\n        v1Count,\n        v2Count,\n        unknownCount\n    };\n}\n/**\r\n * Finds block IDs that match a given list of timestamps.\r\n * @param blocks The array of processed log blocks.\r\n * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.\r\n * @returns An array of block_ids that match the timestamps.\r\n */ function findBlocksByTimestamp(blocks, timestampsToMatch) {\n    const timestampSet = new Set(timestampsToMatch);\n    const matchedBlockIds = [];\n    for (const block of blocks){\n        if (block.start_time) {\n            try {\n                // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'\n                const date = new Date(block.start_time.replace(',', '.'));\n                const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);\n                if (timestampSet.has(blockTimestampInSeconds)) {\n                    matchedBlockIds.push(block.block_id);\n                }\n            } catch (e) {\n                console.error(\"Error parsing date for block \".concat(block.block_id, \":\"), e);\n            }\n        }\n    }\n    return matchedBlockIds;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/logParser.module.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("a23da7ef4eeef7d2")
/******/ })();
/******/ 
/******/ }
);