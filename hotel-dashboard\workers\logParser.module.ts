import { ProcessedLogResults, ProcessedBlock } from './logParser.definitions';
import { extractDataFromBlock } from './dataExtractor.module';

/**
 * 遍历整个日志文件内容，识别数据块边界，并调用数据提取器进行处理。
 * @param logContent 完整的日志内容字符串。
 * @returns 处理后的日志结果。
 */
export function processLogFile(logContent: string): ProcessedLogResults {
  const lines = logContent.split(/\r?\n/);
  const results: ProcessedBlock[] = [];
  let v1Count = 0;
  let v2Count = 0;
  let unknownCount = 0;

  let endOfBlock = -1;

  for (let i = lines.length - 1; i >= 0; i--) {
    // V1 and V2 blocks end with 'insert into g_support'
    if (lines[i].includes('insert into g_support')) {
      console.log(`Found block end at line ${i}: ${lines[i].substring(0, 100)}...`);
      endOfBlock = i;

      let startOfBlock = -1;
      for (let j = endOfBlock; j >= 0; j--) {
        // V1 and V2 blocks start with '开始抽真空'
        if (lines[j].includes('开始抽真空')) {
          console.log(`Found block start at line ${j}: ${lines[j]}`);
          startOfBlock = j;
          break;
        }
      }

      console.log(`Search completed. startOfBlock = ${startOfBlock}, endOfBlock = ${endOfBlock}`);
      if (startOfBlock !== -1) {
        console.log(`Processing block from line ${startOfBlock} to ${endOfBlock} (${endOfBlock - startOfBlock + 1} lines)`);
        const blockLines = lines.slice(startOfBlock, endOfBlock + 1);
        const processedBlock = extractDataFromBlock(blockLines);
        console.log(`Processed block: version=${processedBlock.version}, glue_thickness=${processedBlock.glue_thickness_values.length}, collimation=${processedBlock.collimation_diff_values.length}`);
        results.push(processedBlock);
        
        // Simple version detection for statistics
        // This can be improved if more specific rules are available
        if (processedBlock.version === 'V1') {
          v1Count++;
        } else if (processedBlock.version === 'V2') {
          v2Count++;
        } else {
          unknownCount++;
        }

        // Move the index to the beginning of the found block to avoid re-processing
        i = startOfBlock;
      }
    }
  }

  // Since we are iterating backwards, reverse the results to restore chronological order
  results.reverse();

  return {
    blocks: results,
    totalLines: lines.length,
    v1Count,
    v2Count,
    unknownCount,
  };
}
/**
 * Finds block IDs that match a given list of timestamps.
 * @param blocks The array of processed log blocks.
 * @param timestampsToMatch An array of Unix timestamps (in seconds) to match.
 * @returns An array of block_ids that match the timestamps.
 */
export function findBlocksByTimestamp(
  blocks: ProcessedBlock[],
  timestampsToMatch: number[]
): string[] {
  const timestampSet = new Set(timestampsToMatch);
  const matchedBlockIds: string[] = [];

  for (const block of blocks) {
    if (block.start_time) {
      try {
        // The timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'
        const date = new Date(block.start_time.replace(',', '.'));
        const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);

        if (timestampSet.has(blockTimestampInSeconds)) {
          matchedBlockIds.push(block.block_id);
        }
      } catch (e) {
        console.error(`Error parsing date for block ${block.block_id}:`, e);
      }
    }
  }

  return matchedBlockIds;
}