import { ProcessedLogResults, ProcessedBlock } from './logParser.definitions';
import { extractDataFromBlock } from './dataExtractor.module';

/**
 * @module logParser
 * @description
 * This module is responsible for parsing raw log file content. It identifies distinct data blocks
 * within the log, sends them to the data extractor for detailed processing, and provides
 * utility functions for searching within the processed data. This acts as the primary orchestrator
 * for transforming a raw text log into structured, usable data.
 */

/**
 * Parses a complete log file content string to identify and process individual data blocks.
 *
 * This function implements the core block-finding logic. It operates by iterating through the log
 * lines from bottom to top. It first identifies a potential end-of-block marker (an 'insert into' SQL statement)
 * and then searches backwards from that point to find a corresponding start-of-block marker ('开始抽真空').
 * This "reverse seeking" strategy is effective for logs where blocks are clearly delimited.
 *
 * Once a block's boundaries are identified, the slice of log lines is passed to the
 * `extractDataFromBlock` function, which handles the detailed, version-aware parsing of the block's content.
 *
 * The function also handles various text encodings for the start marker to ensure robustness when
 * processing files that may have been saved with incorrect character sets.
 *
 * @param {string} logContent The entire raw string content of the log file.
 * @returns {ProcessedLogResults} An object containing the array of processed blocks,
 * total line count, and a statistical breakdown of detected block versions (V1, V2, Unknown).
 * The blocks are returned in their original chronological order.
 */
export function processLogFile(logContent: string): ProcessedLogResults {
  const lines = logContent.split(/\r?\n/);
  const results: ProcessedBlock[] = [];
  let v1Count = 0;
  let v2Count = 0;
  let unknownCount = 0;

  // This is a placeholder, not currently used in the loop logic but was part of the original design.
  let endOfBlock = -1;

  // Iterate backwards through the log file. This is an efficient way to find
  // a block's end and then search for its beginning without scanning the whole file multiple times.
  for (let i = lines.length - 1; i >= 0; i--) {
    // The 'insert into' statement is a reliable marker for the end of both V1 and V2 data blocks.
    if (lines[i].includes('insert into')) {
      endOfBlock = i;

      let startOfBlock = -1;
      // Search backwards from the end marker to find the corresponding start marker.
      for (let j = endOfBlock; j >= 0; j--) {
        const line = lines[j];
        // The start marker '开始抽真空' can appear in various garbled forms due to encoding issues.
        // This check tests for the correct string, common mojibake, its Unicode representation,
        // a version with potential garbage characters in between, and an English equivalent.
        if (line.includes('开始抽真空') ||
            line.includes('寮€濮嬫娊鐪熺┖') || // Common mojibake for GBK text read as UTF-8
            line.includes('\u5f00\u59cb\u62bd\u771f\u7a7a') || // Unicode representation
            /开.*始.*抽.*真.*空/.test(line) || // Regex for spaced-out or interrupted characters
            /vacuum.*start/i.test(line)) { // English fallback
          console.log(`找到块开始标记: ${line}`);
          startOfBlock = j;
          break; // Found the start, exit the inner loop.
        }
      }

      // If a valid block (both start and end) was found...
      if (startOfBlock !== -1) {
        // Slice the lines that constitute the block and send for processing.
        const blockLines = lines.slice(startOfBlock, endOfBlock + 1);
        const processedBlock = extractDataFromBlock(blockLines);
        results.push(processedBlock);
        
        // Update statistics based on the version determined by the extractor.
        if (processedBlock.version === 'V1') {
          v1Count++;
        } else if (processedBlock.version === 'V2') {
          v2Count++;
        } else {
          unknownCount++;
        }

        // Optimization: Jump the main loop's index to the beginning of the block
        // we just processed to avoid redundant checks on lines within this block.
        i = startOfBlock;
      }
    }
  }

  // Because we iterated backwards, the results are in reverse chronological order.
  // Reverse them back to restore the original order.
  results.reverse();

  return {
    blocks: results,
    totalLines: lines.length,
    v1Count,
    v2Count,
    unknownCount,
  };
}

/**
 * Searches through an array of processed blocks to find which ones match a given list of timestamps.
 *
 * This function is a utility for features like "search by image name," where a timestamp is
 * extracted from a filename and used to find the corresponding log data. It converts the block's
 * `start_time` string into a Unix timestamp (seconds) and checks for its presence in the provided set.
 *
 * @param {ProcessedBlock[]} blocks The array of processed log blocks to search through.
 * @param {number[]} timestampsToMatch An array of Unix timestamps (in seconds) to find.
 * @returns {string[]} An array of `block_id`s for the blocks that matched a timestamp.
 */
export function findBlocksByTimestamp(
  blocks: ProcessedBlock[],
  timestampsToMatch: number[]
): string[] {
  // Using a Set provides fast, O(1) average time complexity for lookups.
  const timestampSet = new Set(timestampsToMatch);
  const matchedBlockIds: string[] = [];

  for (const block of blocks) {
    if (block.start_time) {
      try {
        // The log timestamp format is 'YYYY-MM-DD HH:MM:SS,sss'.
        // The Date constructor can fail with a comma decimal separator, so it's replaced with a period.
        const date = new Date(block.start_time.replace(',', '.'));
        // Convert to Unix timestamp in seconds to match the input.
        const blockTimestampInSeconds = Math.floor(date.getTime() / 1000);

        if (timestampSet.has(blockTimestampInSeconds)) {
          matchedBlockIds.push(block.block_id);
        }
      } catch (e) {
        console.error(`Error parsing date for block ${block.block_id}:`, e);
      }
    }
  }

  return matchedBlockIds;
}