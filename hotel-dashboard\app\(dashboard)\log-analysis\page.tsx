"use client";

import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import LogDisplayArea from '@/components/log-analysis/LogDisplayArea';
import LogChartView from '@/components/log-analysis/LogChartView';
import LogFileUpload from '@/components/log-analysis/LogFileUpload';
import { SnSearchBar } from '@/components/log-analysis/SnSearchBar';
import ImageNameSearch from '@/components/log-analysis/ImageNameSearch';
import BatchExportCSV from '@/components/log-analysis/BatchExportCSV';
import { parseSnInput } from '@/utils/snHelper';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { generateSingleImageBlob, zipAndDownloadImages, waitForChartReady } from '@/lib/exportUtils';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";

// Define WorkerMessage types locally
type WorkerPostMessage = 
  | { type: 'PARSE_LOG'; payload: { logContent: string } }
  | { type: 'MATCH_BY_TIMESTAMP'; payload: { timestamps: number[] } };

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

export default function LogAnalysisPage() {
  const [processedData, setProcessedData] = useState<ProcessedBlock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dataChunks, setDataChunks] = useState<ProcessedBlock[]>([]);
  
  // Unified selection state
  const [selectedBlockIds, setSelectedBlockIds] = useState<Set<string>>(new Set());

  // SN Search states
  const [searchQuery, setSearchQuery] = useState('');
  const [isSnSearching, setIsSnSearching] = useState(false);

  // Image Name Search states
  const [isImageNameSearching, setIsImageNameSearching] = useState(false);

  // --- Queued export states ---
  const [exportQueue, setExportQueue] = useState<string[]>([]);
  const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = useState<string | null>(null);
  const [generatedImages, setGeneratedImages] = useState<{ filename: string; blob: Blob }[]>([]);
  const [exportProgress, setExportProgress] = useState<{ completed: number; total: number } | null>(null);
  
  const exportTargetContainerRef = useRef<HTMLDivElement>(null);
  const logParserWorker = useRef<Worker | null>(null);
  const { toast } = useToast();

  // --- Worker Initialization and Message Handling ---
  useEffect(() => {
    logParserWorker.current = new Worker(new URL('@/workers/logParser.worker.ts', import.meta.url));

    logParserWorker.current.onmessage = (event) => {
      console.log('[Main Thread] Received message from worker:', event.data);
      // Destructure all potential properties from the message
      const { type, payload, error, allBlocks, matchedBlockIds } = event.data;
      
      switch (type) {
        case 'PARSE_LOG_RESULT':
          setIsLoading(false);
          if (error) {
            handleError(error);
          } else {
            // Use allBlocks directly, as per previous bug fixes.
            handleDataProcessed(allBlocks || []);
          }
          break;
        case 'MATCH_BY_TIMESTAMP_RESULT':
          setIsImageNameSearching(false);
          if (error) {
            toast({ title: "图片名称搜索失败", description: error, variant: "destructive" });
          } else {
            // Ensure matchedBlockIds is handled correctly, even if payload is not the primary source
            const ids = (payload && payload.matchedBlockIds) || matchedBlockIds || [];
            setSelectedBlockIds(prevIds => new Set([...Array.from(prevIds), ...ids]));
            toast({
              title: "图片名称搜索完成",
              description: `匹配到 ${ids.length} 个新的数据块。`,
            });
          }
          break;
        default:
          break;
      }
    };

    return () => {
      logParserWorker.current?.terminate();
    };
  }, [toast]);

  const handleFileProcessed = (logContent: string) => {
    if (!logParserWorker.current) {
      handleError("日志解析器未初始化。");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSelectedBlockIds(new Set());
    setProcessedData([]); // Reset processed data
    setDataChunks([]); // Also reset legacy dataChunks if needed
    toast({
      title: "开始处理",
      description: "正在分析日志文件...",
    });

    const message: WorkerPostMessage = { type: 'PARSE_LOG', payload: { logContent } };
    logParserWorker.current.postMessage(message);
  };

  const handleDataProcessed = (workerData: WorkerProcessedBlock[]) => {
    const processedData: ProcessedBlock[] = workerData.map(block => ({
      ...block,
      data: []
    }));
    setProcessedData(processedData);
    setDataChunks(processedData); // Keep dataChunks in sync for now
    toast({ title: "处理完成", description: "日志文件已成功解析。" });
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    toast({ title: "处理错误", description: errorMessage, variant: "destructive" });
  };

  const handleBlockSelectionChanged = useCallback((selectedIds: Set<string>) => {
    setSelectedBlockIds(selectedIds);
  }, []);

  const selectedBlocks = useMemo(() => {
    return processedData.filter(block => selectedBlockIds.has(block.block_id));
  }, [processedData, selectedBlockIds]);

  const chartDataForView = useMemo(() => {
    return selectedBlocks;
  }, [selectedBlocks]);

  const handleSnSearch = (query: string) => {
    setSearchQuery(query);
    setIsSnSearching(true);

    const snsToSearch = parseSnInput(query).map(sn => sn.toUpperCase());
    if (snsToSearch.length === 0) {
      setSelectedBlockIds(new Set());
      setIsSnSearching(false);
      return;
    }

    const results = new Set<string>();
    processedData.forEach(block => {
      const snFromId = block.block_id.split('_')[0].toUpperCase();
      for (const sn of snsToSearch) {
        const isSnInSnsArray = Array.isArray(block.sns) && block.sns.some(blockSn => blockSn.toUpperCase() === sn);
        if (snFromId === sn || isSnInSnsArray) {
          results.add(block.block_id);
          break;
        }
      }
    });
    setSelectedBlockIds(results);
    toast({
      title: "SN搜索完成",
      description: `找到 ${results.size} 个相关数据块。`,
    });
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSelectedBlockIds(new Set());
    setIsSnSearching(false);
  };

  const generateExportFilename = (block: ProcessedBlock): string => {
    if (!block) return `unknown_block.png`;
  
    // 1. Format Timestamp
    let timestampPart = 'NODATE';
    if (block.start_time) {
      try {
        const date = new Date(block.start_time.replace(',', '.'));
        const y = date.getFullYear();
        const m = (date.getMonth() + 1).toString().padStart(2, '0');
        const d = date.getDate().toString().padStart(2, '0');
        const h = date.getHours().toString().padStart(2, '0');
        const min = date.getMinutes().toString().padStart(2, '0');
        const s = date.getSeconds().toString().padStart(2, '0');
        timestampPart = `${y}${m}${d}_${h}_${min}_${s}`;
      } catch (e) {
        // Keep 'NODATE' on parsing error
      }
    }
  
    // 2. Find SN
    let snPart = 'NOSN';
    if (Array.isArray(block.sns) && block.sns.length > 0 && block.sns[0]) {
      snPart = block.sns[0];
    } else {
      const snFromId = block.block_id.split('_')[0];
      if (snFromId && snFromId.toLowerCase() !== 'block') {
        snPart = snFromId;
      }
    }
  
    return `${timestampPart}GBSN${snPart}.png`;
  };
  
  const handleImageNameSearch = (timestamps: number[]) => {
    if (!logParserWorker.current) {
        toast({ title: "错误", description: "日志解析器未初始化。", variant: "destructive" });
        return;
    }
    if (timestamps.length === 0) {
        toast({ title: "提示", description: "没有从文件名中解析出有效的时间戳。", variant: "default" });
        return;
    }
    
    setIsImageNameSearching(true);
    toast({ title: "正在搜索...", description: `根据 ${timestamps.length} 个时间戳进行匹配。` });
    
    const message: WorkerPostMessage = { type: 'MATCH_BY_TIMESTAMP', payload: { timestamps } };
    logParserWorker.current.postMessage(message);
  };

  const displayedDataChunks = useMemo(() => {
    if (!isSnSearching) {
      return processedData;
    }
    return processedData.filter(chunk => selectedBlockIds.has(chunk.block_id));
  }, [isSnSearching, processedData, selectedBlockIds]);
 
   const initiateExportProcess = (exportIds: string[]) => {
    if (exportIds.length === 0) {
      toast({
        title: "没有内容可导出",
        description: "请选择至少一个数据块进行导出。",
        variant: "default",
      });
      return;
    }
    
    setExportProgress({ completed: 0, total: exportIds.length });
    setGeneratedImages([]);
    setExportQueue([...exportIds]);
    setCurrentlyExportingBlockId(exportIds[0]);

    toast({
      title: "导出已开始",
      description: `准备导出 ${exportIds.length} 个图表...`,
    });
  };

  useEffect(() => {
    if (!currentlyExportingBlockId) return;
  
    const processBlock = async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      const container = exportTargetContainerRef.current;
      if (!container) {
        console.error("Export container not found.");
        return;
      }
  
      const blockToExport = processedData.find(b => b.block_id === currentlyExportingBlockId);
      if (!blockToExport) {
        console.error(`Block with ID ${currentlyExportingBlockId} not found in dataChunks.`);
        // Move to the next item in the queue even if the block is not found
        setExportQueue(prevQueue => {
            const newQueue = prevQueue.slice(1);
            setCurrentlyExportingBlockId(newQueue[0] || null);
            return newQueue;
        });
        return;
      }
  
      const chartElement = container.querySelector(`[data-block-id="${currentlyExportingBlockId}"]`) as HTMLElement;
      if (chartElement) {
        try {
          await waitForChartReady(chartElement);
          const blob = await generateSingleImageBlob(chartElement);
          const filename = generateExportFilename(blockToExport);
          setGeneratedImages(prev => [...prev, { filename, blob }]);
        } catch (error) {
          toast({ title: "图表生成失败", description: `无法为数据块 ${currentlyExportingBlockId} 生成图片。`, variant: "destructive" });
        }
      } else {
          console.warn(`Chart element for block ID ${currentlyExportingBlockId} not found in DOM.`);
      }
  
      // Advance the queue
      setExportQueue(prevQueue => {
        const newQueue = prevQueue.slice(1);
        setCurrentlyExportingBlockId(newQueue[0] || null);
        return newQueue;
      });
    };
  
    processBlock();
  }, [currentlyExportingBlockId, processedData, toast]);

  useEffect(() => {
    if (exportProgress) {
      setExportProgress(prev => ({ ...prev!, completed: generatedImages.length }));
    }
  }, [generatedImages]);

  useEffect(() => {
    if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {
       if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {
        const zipAndDownload = async () => {
          try {
            await zipAndDownloadImages(generatedImages, 'exported_log_charts');
            toast({ title: "导出成功", description: `已将 ${generatedImages.length} 个图表导出为压缩包。` });
          } catch (error) {
            toast({ title: "导出失败", description: "无法创建或下载ZIP文件。", variant: "destructive" });
          } finally {
            setExportQueue([]);
            setCurrentlyExportingBlockId(null);
            setGeneratedImages([]);
            setExportProgress(null);
          }
        };
        zipAndDownload();
      } else if (exportProgress.total > 0) {
         toast({
            title: "导出完成",
            description: `成功导出 ${generatedImages.length} 个图表，${exportProgress.total - generatedImages.length} 个失败。`,
            variant: "default"
         });
         setExportQueue([]);
         setCurrentlyExportingBlockId(null);
         setGeneratedImages([]);
         setExportProgress(null);
      }
    }
  }, [currentlyExportingBlockId, exportProgress, generatedImages, exportQueue.length, toast]);

  const blockToRenderOffscreen = useMemo(() => {
    if (!currentlyExportingBlockId) return null;
    return processedData.find(b => b.block_id === currentlyExportingBlockId) || null;
  }, [currentlyExportingBlockId, processedData]);

  return (
    <div className="flex flex-col h-full p-4 gap-4">
      <h1 className="text-2xl font-bold">日志分析与查询</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1 space-y-4">
          <LogFileUpload
            onFileProcessed={handleFileProcessed}
            isProcessing={isLoading}
            disabled={isLoading || !!exportProgress || isImageNameSearching}
          />
           <SnSearchBar
             onSearch={handleSnSearch}
             onClear={handleClearSearch}
             isLoading={isLoading || isImageNameSearching}
           />
           <ImageNameSearch 
             onSearch={handleImageNameSearch}
             isLoading={isImageNameSearching}
             disabled={processedData.length === 0 || isLoading || !!exportProgress}
           />
        </div>
        <div className="md:col-span-2">
          <LogDisplayArea
            dataChunks={displayedDataChunks}
            onSelectionChange={handleBlockSelectionChanged}
            onStartExport={initiateExportProcess}
            selectedBlockIds={selectedBlockIds}
            isSearching={isSnSearching}
          />
        </div>
      </div>

      {exportProgress && (
        <Alert>
          <AlertTitle>正在导出图表...</AlertTitle>
          <AlertDescription>
            <div className="flex items-center gap-2 mt-2">
              <Progress value={(exportProgress.completed / exportProgress.total) * 100} className="w-full" />
              <span>{`${exportProgress.completed} / ${exportProgress.total}`}</span>
            </div>
          </AlertDescription>
        </Alert>
      )}
      
      <div className="mt-4">
        <BatchExportCSV selectedBlocks={selectedBlocks} />
      </div>

      <div className="flex-grow mt-4">
        {isLoading ? (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center">
              <p className="text-muted-foreground">正在处理文件...</p>
            </CardContent>
          </Card>
        ) : error ? (
          <Card className="h-full flex items-center justify-center bg-destructive/10">
            <CardContent className="text-center">
              <p className="text-destructive font-semibold">发生错误</p>
              <p className="text-muted-foreground">{error}</p>
            </CardContent>
          </Card>
        ) : isSnSearching && displayedDataChunks.length === 0 ? (
           <Card className="h-full flex items-center justify-center">
             <CardContent className="text-center">
               <p className="text-muted-foreground">
                 未找到与 "{searchQuery}" 相关的日志块。
               </p>
             </CardContent>
           </Card>
        ) : chartDataForView.length > 0 ? (
          <LogChartView
            dataChunks={chartDataForView}
            selectedBlockIds={Array.from(selectedBlockIds)}
            onBlockSelect={() => {}}
            isHighlighted={isSnSearching}
          />
        ) : (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center">
              <p className="text-muted-foreground">
                {processedData.length > 0
                  ? "请从左侧选择数据块以显示图表"
                  : "请先上传日志文件"}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Offscreen rendering container */}
      <div 
        ref={exportTargetContainerRef} 
        style={{ position: 'absolute', left: '-9999px', top: '-9999px', width: '1200px', height: '800px' }}
      >
        {blockToRenderOffscreen && (
          <LogChartView
            dataChunks={[blockToRenderOffscreen]}
            selectedBlockIds={[blockToRenderOffscreen.block_id]}
            onBlockSelect={() => {}}
          />
        )}
      </div>
    </div>
  );
}