"use client";

import React, { useState, useMemo, useRef, useEffect, useCallback } from 'react';
import LogDisplayArea from '@/components/log-analysis/LogDisplayArea';
import LogChartView from '@/components/log-analysis/LogChartView';
import LogFileUpload from '@/components/log-analysis/LogFileUpload';
import { UnifiedLogSearch } from '@/components/log-analysis/UnifiedLogSearch';
import BatchExportCSV from '@/components/log-analysis/BatchExportCSV';
import { Card, CardContent } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { generateSingleImageBlob, zipAndDownloadImages, waitForChartReady, exportSnDataToXlsx } from '@/lib/exportUtils';
import { ProcessedBlock as WorkerProcessedBlock } from '@/workers/logParser.definitions';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";

// Define WorkerMessage types locally
type WorkerPostMessage = 
  | { type: 'PARSE_LOG'; payload: { logContent: string } }
  | { type: 'MATCH_BY_TIMESTAMP'; payload: { timestamps: number[] } };

interface ChartDataItem {
  name: string;
  value: number;
  type: string;
  block_id: string;
}

interface ProcessedBlock extends WorkerProcessedBlock {
  data: ChartDataItem[];
}

export default function LogAnalysisPage() {
  const [processedData, setProcessedData] = useState<ProcessedBlock[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dataChunks, setDataChunks] = useState<ProcessedBlock[]>([]);
  
  // Unified selection state
  const [selectedBlockIds, setSelectedBlockIds] = useState<Set<string>>(new Set());

  // Unified Search state
  const [isSearching, setIsSearching] = useState(false);

  // --- Queued export states ---
  const [exportQueue, setExportQueue] = useState<string[]>([]);
  const [currentlyExportingBlockId, setCurrentlyExportingBlockId] = useState<string | null>(null);
  const [generatedImages, setGeneratedImages] = useState<{ filename: string; blob: Blob }[]>([]);
  const [exportProgress, setExportProgress] = useState<{ completed: number; total: number } | null>(null);
  
  const exportTargetContainerRef = useRef<HTMLDivElement>(null);
  const chartRefs = useRef<Map<string, HTMLDivElement | null>>(new Map());
  const logParserWorker = useRef<Worker | null>(null);
  const [blockToRenderOffscreen, setBlockToRenderOffscreen] = useState<ProcessedBlock | null>(null);
  const [renderCallback, setRenderCallback] = useState<(() => void) | null>(null);
  const { toast } = useToast();

  // --- Worker Initialization and Message Handling ---
  useEffect(() => {
    logParserWorker.current = new Worker(new URL('@/workers/logParser.worker.ts', import.meta.url));

    logParserWorker.current.onmessage = (event) => {
      console.log('[Main Thread] Received message from worker:', event.data);
      // Destructure all potential properties from the message
      const { type, payload, error, allBlocks, matchedBlockIds } = event.data;
      
      switch (type) {
        case 'PARSE_LOG_RESULT':
          setIsLoading(false);
          if (error) {
            handleError(error);
          } else {
            handleDataProcessed(allBlocks || []);
          }
          break;
        // NOTE: The 'MATCH_BY_TIMESTAMP_RESULT' case is removed as the new component handles this logic.
        // The new component will call onSearch with the extracted search terms.
        default:
          break;
      }
    };

    return () => {
      logParserWorker.current?.terminate();
    };
  }, [toast]);

  const handleFileProcessed = (logContent: string) => {
    if (!logParserWorker.current) {
      handleError("日志解析器未初始化。");
      return;
    }

    setIsLoading(true);
    setError(null);
    setSelectedBlockIds(new Set());
    setProcessedData([]); // Reset processed data
    setDataChunks([]); // Also reset legacy dataChunks if needed
    toast({
      title: "开始处理",
      description: "正在分析日志文件...",
    });

    const message: WorkerPostMessage = { type: 'PARSE_LOG', payload: { logContent } };
    logParserWorker.current.postMessage(message);
  };

  const handleDataProcessed = (workerData: WorkerProcessedBlock[]) => {
    const processedData: ProcessedBlock[] = workerData.map(block => ({
      ...block,
      data: []
    }));
    setProcessedData(processedData);
    setDataChunks(processedData); // Keep dataChunks in sync for now
    toast({ title: "处理完成", description: "日志文件已成功解析。" });
  };

  const handleError = (errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
    toast({ title: "处理错误", description: errorMessage, variant: "destructive" });
  };

  const handleBlockSelectionChanged = useCallback((selectedIds: Set<string>) => {
    setSelectedBlockIds(selectedIds);
    if (isSearching && selectedIds.size === 0) {
      setIsSearching(false);
    }
  }, [isSearching]);

  const selectedBlocks = useMemo(() => {
    return processedData.filter(block => selectedBlockIds.has(block.block_id));
  }, [processedData, selectedBlockIds]);

  const chartDataForView = useMemo(() => {
    return selectedBlocks;
  }, [selectedBlocks]);

  const handleSearch = (searchTerms: string[]) => {
    setIsSearching(true);
    if (searchTerms.length === 0) {
      setSelectedBlockIds(new Set());
      setIsSearching(false);
      return;
    }

    const results = new Set<string>();

    // Normalize search terms for efficient, case-insensitive lookup
    const lowerCaseSearchTermSet = new Set(
      searchTerms.map(term => term.toLowerCase().trim()).filter(term => term !== '')
    );

    if (lowerCaseSearchTermSet.size === 0) {
        setSelectedBlockIds(new Set());
        setIsSearching(false);
        return;
    }

    processedData.forEach(block => {
      const lowerCaseBlockId = block.block_id.toLowerCase();
      
      lowerCaseSearchTermSet.forEach(term => {
        //console.log(`[DEBUG] 正在搜索: "${term}" 于 Block ID: "${lowerCaseBlockId}"`);
        if (lowerCaseBlockId.includes(term)) {
          //console.log(`[DEBUG] >> 匹配成功! <<`);
          results.add(block.block_id);
        }
      });
    });

    setSelectedBlockIds(results);
    toast({
      title: "搜索完成",
      description: `找到 ${results.size} 个匹配的数据块。`,
    });
  };

  const handleClear = () => {
    setSelectedBlockIds(new Set());
    setIsSearching(false);
  };

  const generateExportFilename = (block: ProcessedBlock): string => {
    if (!block) return `unknown_block.png`;
  
    // 1. Format Timestamp
    let timestampPart = 'NODATE';
    if (block.start_time) {
      try {
        const date = new Date(block.start_time.replace(',', '.'));
        const y = date.getFullYear();
        const m = (date.getMonth() + 1).toString().padStart(2, '0');
        const d = date.getDate().toString().padStart(2, '0');
        const h = date.getHours().toString().padStart(2, '0');
        const min = date.getMinutes().toString().padStart(2, '0');
        const s = date.getSeconds().toString().padStart(2, '0');
        timestampPart = `${y}${m}${d}_${h}_${min}_${s}`;
      } catch (e) {
        // Keep 'NODATE' on parsing error
      }
    }
  
    // 2. Find SN
    let snPart = 'NOSN';
    if (Array.isArray(block.sns) && block.sns.length > 0 && block.sns[0]) {
      snPart = block.sns[0];
    } else {
      const snFromId = block.block_id.split('_')[0];
      if (snFromId && snFromId.toLowerCase() !== 'block') {
        snPart = snFromId;
      }
    }
  
    return `${timestampPart}GBSN${snPart}.png`;
  };
  
  const displayedDataChunks = useMemo(() => {
    if (!isSearching) {
      return processedData;
    }
    return processedData.filter(chunk => selectedBlockIds.has(chunk.block_id));
  }, [isSearching, processedData, selectedBlockIds]);
 
   const initiateExportProcess = (exportIds: string[]) => {
    if (exportIds.length === 0) {
      toast({
        title: "没有内容可导出",
        description: "请选择至少一个数据块进行导出。",
        variant: "default",
      });
      return;
    }
    
    setExportProgress({ completed: 0, total: exportIds.length });
    setGeneratedImages([]);
    setExportQueue([...exportIds]);
    setCurrentlyExportingBlockId(exportIds[0]);

    toast({
      title: "导出已开始",
      description: `准备导出 ${exportIds.length} 个图表...`,
    });
  };

  useEffect(() => {
    if (!currentlyExportingBlockId) return;
  
    const processBlock = async () => {
      await new Promise(resolve => setTimeout(resolve, 100));
      const container = exportTargetContainerRef.current;
      if (!container) {
        console.error("Export container not found.");
        return;
      }
  
      const blockToExport = processedData.find(b => b.block_id === currentlyExportingBlockId);
      if (!blockToExport) {
        console.error(`Block with ID ${currentlyExportingBlockId} not found in dataChunks.`);
        // Move to the next item in the queue even if the block is not found
        setExportQueue(prevQueue => {
            const newQueue = prevQueue.slice(1);
            setCurrentlyExportingBlockId(newQueue[0] || null);
            return newQueue;
        });
        return;
      }
  
      const chartElement = container.querySelector(`[data-block-id="${currentlyExportingBlockId}"]`) as HTMLElement;
      if (chartElement) {
        try {
          await waitForChartReady(chartElement);
          const blob = await generateSingleImageBlob(chartElement);
          const filename = generateExportFilename(blockToExport);
          setGeneratedImages(prev => [...prev, { filename, blob }]);
        } catch (error) {
          toast({ title: "图表生成失败", description: `无法为数据块 ${currentlyExportingBlockId} 生成图片。`, variant: "destructive" });
        }
      } else {
          console.warn(`Chart element for block ID ${currentlyExportingBlockId} not found in DOM.`);
      }
  
      // Advance the queue
      setExportQueue(prevQueue => {
        const newQueue = prevQueue.slice(1);
        setCurrentlyExportingBlockId(newQueue[0] || null);
        return newQueue;
      });
    };
  
    processBlock();
  }, [currentlyExportingBlockId, processedData, toast]);

  useEffect(() => {
    if (exportProgress) {
      setExportProgress(prev => ({ ...prev!, completed: generatedImages.length }));
    }
  }, [generatedImages]);

  useEffect(() => {
    if (exportProgress && !currentlyExportingBlockId && exportQueue.length === 0) {
       if (generatedImages.length > 0 && generatedImages.length === exportProgress.total) {
        const zipAndDownload = async () => {
          try {
            await zipAndDownloadImages(generatedImages, 'exported_log_charts');
            toast({ title: "导出成功", description: `已将 ${generatedImages.length} 个图表导出为压缩包。` });
          } catch (error) {
            toast({ title: "导出失败", description: "无法创建或下载ZIP文件。", variant: "destructive" });
          } finally {
            setExportQueue([]);
            setCurrentlyExportingBlockId(null);
            setGeneratedImages([]);
            setExportProgress(null);
          }
        };
        zipAndDownload();
      } else if (exportProgress.total > 0) {
         toast({
            title: "导出完成",
            description: `成功导出 ${generatedImages.length} 个图表，${exportProgress.total - generatedImages.length} 个失败。`,
            variant: "default"
         });
         setExportQueue([]);
         setCurrentlyExportingBlockId(null);
         setGeneratedImages([]);
         setExportProgress(null);
      }
    }
  }, [currentlyExportingBlockId, exportProgress, generatedImages, exportQueue.length, toast]);

  const generateChartForBlock = async (block: ProcessedBlock): Promise<string | undefined> => {
    return new Promise(async (resolvePromise) => {
      const onChartReady = async () => {
        try {
          const container = document.getElementById('offscreen-chart-container');
          if (!container) {
            throw new Error("离屏渲染容器 'offscreen-chart-container' 未找到。");
          }
          await new Promise(res => setTimeout(res, 300));
          const imageBlob = await generateSingleImageBlob(container);
          
          // Convert blob to base64
          const reader = new FileReader();
          reader.onloadend = () => {
            const base64data = reader.result;
            console.log(`[XLSX Export] Chart image generated for block ${block.block_id}`);
            resolvePromise(base64data as string);
          };
          reader.onerror = (error) => {
             console.error(`[XLSX Export] Failed to read blob for block ${block.block_id}:`, error);
             resolvePromise(undefined);
          };
          reader.readAsDataURL(imageBlob);

        } catch (error) {
          console.error(`[XLSX Export] Image generation failed for block ${block.block_id}:`, error);
          toast({
            title: "图表生成失败",
            description: `无法为数据块 ${block.block_id} 生成图表。`,
            variant: "destructive",
          });
          resolvePromise(undefined);
        } finally {
          setRenderCallback(null);
          setBlockToRenderOffscreen(null);
        }
      };

      setRenderCallback(() => onChartReady);
      setBlockToRenderOffscreen(block);
    });
  };

  const handleExportXlsx = async () => {
    if (selectedBlocks.length === 0) {
      toast({
        title: "没有内容可导出",
        description: "请选择至少一个数据块以导出为 XLSX。",
        variant: "default",
      });
      return;
    }

    toast({
      title: "正在生成 XLSX 文件...",
      description: `准备处理 ${selectedBlocks.length} 个数据块。这可能需要一些时间。`,
    });

    setIsLoading(true);

    try {
      const exportData = [];

      for (const block of selectedBlocks) {
        try {
          // --- OQC Query Logic Start ---
          let oqcValue: any = 'N/A';
          const sn = block.sns?.[0];
          let sourceTable: string | null = null;

          if (sn) {
            const firstChar = sn.charAt(0).toUpperCase();
            if (firstChar === 'G') {
              sourceTable = 'g_support';
            } else if (firstChar === 'H') {
              sourceTable = 'h_support';
            }
          }

          if (sourceTable && sn) {
            try {
              const response = await fetch('/api/oqc-query', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ tableName: sourceTable, sn: sn })
              });
              if (response.ok) {
                const result = await response.json();
                oqcValue = result.diff ?? 'N/A';
              } else {
                console.warn(`API call for OQC value failed for SN ${sn} with status: ${response.status}`);
              }
            } catch (apiError) {
              console.error(`Error fetching OQC value for SN ${sn}:`, apiError);
            }
          }
          // --- OQC Query Logic End ---

          const chartImageBase64 = await generateChartForBlock(block);

          const rowObject = {
            "Timestamp": block.start_time || 'N/A',
            "SN": block.sns.join(', ') || 'N/A',
            "破真空前准直": block.preVentCollimatorDiff ?? 'N/A',
            "终值": block.collimatorDiffAfterUV ?? 'N/A',
            "OQC准直": oqcValue, // Add the queried OQC value
            "Chart": chartImageBase64,
          };
          exportData.push(rowObject);
        } catch (e) {
          console.error(`[XLSX Export] Failed to process block ${block.block_id}:`, e);
          toast({
            title: "数据块处理失败",
            description: `在为 ${block.block_id} 生成图表时发生错误。`,
            variant: "destructive",
          });
          // Optionally, add a placeholder for the failed block
          exportData.push({
            "Timestamp": block.start_time || 'N/A',
            "SN": block.sns.join(', ') || 'N/A',
            "破真空前准直": block.preVentCollimatorDiff ?? 'N/A',
            "终值": block.collimatorDiffAfterUV ?? 'N/A',
            "OQC准直": 'QUERY FAILED', // Indicate failure
            "Chart": "GENERATION FAILED",
          });
        }
      }

      if (exportData.length > 0) {
        // 1. Generate timestamp
        const now = new Date();
        const timestamp = `${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}_${now.getHours().toString().padStart(2, '0')}${now.getMinutes().toString().padStart(2, '0')}${now.getSeconds().toString().padStart(2, '0')}`;

        // 2. Create new filename
        const fileNameWithTimestamp = `Exported_Log_Data_${timestamp}`;

        // 3. Call the export function with the new name
        await exportSnDataToXlsx(exportData, fileNameWithTimestamp);
        
        toast({
          title: "XLSX 导出成功",
          description: "文件已开始下载。",
        });
      } else {
        toast({
          title: "没有数据可导出",
          description: "所有选定的数据块都无法处理。",
        });
      }

    } catch (error) {
      console.error("XLSX export failed:", error);
      toast({
        title: "XLSX 导出失败",
        description: error instanceof Error ? error.message : "发生未知错误。",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setBlockToRenderOffscreen(null);
    }
  };

  return (
    <div className="flex flex-col h-full p-4 gap-4">
      <h1 className="text-2xl font-bold">日志分析与查询</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-1 space-y-4">
          <LogFileUpload
            onFileProcessed={handleFileProcessed}
            isProcessing={isLoading}
            disabled={isLoading || !!exportProgress || isSearching}
          />
          <UnifiedLogSearch
            onSearch={handleSearch}
            onClear={handleClear}
            isLoading={isSearching}
            disabled={processedData.length === 0 || isLoading || !!exportProgress}
          />
        </div>
        <div className="md:col-span-2">
          <LogDisplayArea
            dataChunks={displayedDataChunks}
            onSelectionChange={handleBlockSelectionChanged}
            onStartExport={initiateExportProcess}
            onExportXlsx={handleExportXlsx}
            selectedBlockIds={selectedBlockIds}
            isSearching={isSearching}
          />
        </div>
      </div>

      {exportProgress && (
        <Alert>
          <AlertTitle>正在导出图表...</AlertTitle>
          <AlertDescription>
            <div className="flex items-center gap-2 mt-2">
              <Progress value={(exportProgress.completed / exportProgress.total) * 100} className="w-full" />
              <span>{`${exportProgress.completed} / ${exportProgress.total}`}</span>
            </div>
          </AlertDescription>
        </Alert>
      )}
      

      <div className="flex-grow mt-4">
        {isLoading ? (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center">
              <p className="text-muted-foreground">正在处理文件...</p>
            </CardContent>
          </Card>
        ) : error ? (
          <Card className="h-full flex items-center justify-center bg-destructive/10">
            <CardContent className="text-center">
              <p className="text-destructive font-semibold">发生错误</p>
              <p className="text-muted-foreground">{error}</p>
            </CardContent>
          </Card>
        ) : isSearching && displayedDataChunks.length === 0 ? (
           <Card className="h-full flex items-center justify-center">
             <CardContent className="text-center">
               <p className="text-muted-foreground">
                 未找到相关的日志块。
               </p>
             </CardContent>
           </Card>
        ) : chartDataForView.length > 0 ? (
          <LogChartView
            ref={exportTargetContainerRef}
            chartRefs={chartRefs}
            dataChunks={chartDataForView}
            selectedBlockIds={Array.from(selectedBlockIds)}
            onBlockSelect={() => {}}
            isHighlighted={isSearching}
          />
        ) : (
          <Card className="h-full flex items-center justify-center">
            <CardContent className="text-center">
              <p className="text-muted-foreground">
                {processedData.length > 0
                  ? "请从左侧选择数据块以显示图表"
                  : "请先上传日志文件"}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Offscreen rendering container */}
      <div 
        id="offscreen-chart-container"
        style={{ position: 'absolute', left: '-9999px', top: '-9999px', width: '1200px', height: '800px', overflow: 'hidden' }}
      >
        {blockToRenderOffscreen && (
          <LogChartView
            dataChunks={[blockToRenderOffscreen]}
            selectedBlockIds={[blockToRenderOffscreen.block_id]}
            onBlockSelect={() => {}}
            isHighlighted={false}
            onRenderComplete={renderCallback || undefined}
          />
        )}
      </div>
    </div>
  );
}