const fs = require('fs');

// Read the file with different encodings
console.log('=== Testing file encoding ===');

try {
    // Try UTF-8 (default)
    const utf8Content = fs.readFileSync('v2.txt', 'utf8');
    const utf8Lines = utf8Content.split('\n');
    console.log('UTF-8 first line:', utf8Lines[0]);
    console.log('UTF-8 contains "开始抽真空":', utf8Lines[0].includes('开始抽真空'));
    
    // Try GBK/GB2312
    const gbkContent = fs.readFileSync('v2.txt', 'latin1');
    const gbkLines = gbkContent.split('\n');
    console.log('Latin1 first line:', gbkLines[0]);
    
    // Check for the specific pattern we saw in the logs
    console.log('UTF-8 contains "寮€濮嬫娊鐪熺┖":', utf8Lines[0].includes('寮€濮嬫娊鐪熺┖'));
    
} catch (error) {
    console.error('Error reading file:', error);
}
