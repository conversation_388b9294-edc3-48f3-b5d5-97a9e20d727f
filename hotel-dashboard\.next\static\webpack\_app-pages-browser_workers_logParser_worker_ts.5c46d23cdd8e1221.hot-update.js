"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_workers_logParser_worker_ts",{

/***/ "(app-pages-browser)/./workers/dataExtractor.module.ts":
/*!*****************************************!*\
  !*** ./workers/dataExtractor.module.ts ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractDataFromBlock: () => (/* binding */ extractDataFromBlock)\n/* harmony export */ });\n/* harmony import */ var _rulesEngine__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rulesEngine */ \"(app-pages-browser)/./workers/rulesEngine.ts\");\n/* harmony import */ var _utils_snHelper__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/snHelper */ \"(app-pages-browser)/./utils/snHelper.ts\");\n\n\n/**\r\n * Generates a descriptive name for a log block based on its start time and SN.\r\n * Format: yyyyMMdd_HHmmss\r\n * @param timestamp The timestamp string (e.g., \"2023-01-01 12:30:00,123\").\r\n * @returns A formatted block ID.\r\n */ const generateBlockId = (timestamp)=>{\n    if (!timestamp) {\n        return \"NO_TIMESTAMP_\".concat(Date.now());\n    }\n    const date = new Date(timestamp.replace(',', '.')); // Handle comma decimal separator\n    if (isNaN(date.getTime())) {\n        return \"INVALID_TIMESTAMP_\".concat(Date.now());\n    }\n    const y = date.getFullYear().toString().padStart(4, '0');\n    const m = (date.getMonth() + 1).toString().padStart(2, '0');\n    const d = date.getDate().toString().padStart(2, '0');\n    const h = date.getHours().toString().padStart(2, '0');\n    const min = date.getMinutes().toString().padStart(2, '0');\n    const s = date.getSeconds().toString().padStart(2, '0');\n    return \"\".concat(y).concat(m).concat(d, \"_\").concat(h, \"_\").concat(min, \"_\").concat(s);\n};\n/**\r\n * Extracts all relevant data from a given block of log lines into a structured ProcessedBlock.\r\n * @param blockLines An array of strings, where each string is a line from the log.\r\n * @returns A ProcessedBlock object containing all extracted data.\r\n */ const extractDataFromBlock = (blockLines)=>{\n    const entireBlock = blockLines.join('\\n');\n    const collimationData = [];\n    const glueThicknessData = [];\n    const vacuumPumpEvents = [];\n    const ventValveEvents = [];\n    let startTime = null;\n    let endTime = null;\n    let lastSeenTimestamp = null;\n    const sn = (0,_utils_snHelper__WEBPACK_IMPORTED_MODULE_1__.findSN)(entireBlock);\n    let version = 'UNKNOWN';\n    if (entireBlock.includes('####### 胶厚值:')) {\n        version = 'V1';\n    } else if (entireBlock.includes('ColimatorX13_preUV')) {\n        version = 'V2';\n    }\n    console.log(\"Version detected: \".concat(version, \" (\").concat(blockLines.length, \" lines)\"));\n    let inV2ChartDataSection = false;\n    console.log(\"Processing \".concat(blockLines.length, \" lines for version \").concat(version));\n    for (const line of blockLines){\n        const timestampMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_TIMESTAMP);\n        if (timestampMatch && timestampMatch[1]) {\n            lastSeenTimestamp = timestampMatch[1];\n            if (!startTime) {\n                startTime = lastSeenTimestamp;\n            }\n            endTime = lastSeenTimestamp;\n        }\n        if (version === 'V2') {\n            // V2 data collection starts from '轴停止运动'\n            if (line.includes('轴停止运动')) {\n                console.log(\"V2: Entering chart data section at line: \".concat(line));\n                inV2ChartDataSection = true;\n            }\n        }\n        if (version === 'V1') {\n            // --- V1 DATA EXTRACTION ---\n            const glueMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V1_GLUE_THICKNESS);\n            if (glueMatch && glueMatch[1] && lastSeenTimestamp) {\n                glueThicknessData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(glueMatch[1])\n                });\n            }\n            const collimationMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V1_COLLIMATION);\n            if (collimationMatch && collimationMatch[1] && lastSeenTimestamp) {\n                collimationData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(collimationMatch[1])\n                });\n            }\n        } else if (version === 'V2' && inV2ChartDataSection) {\n            // --- V2 DATA EXTRACTION (within the correct section) ---\n            const glueMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V2_GLUE_THICKNESS); // Use the imported regex\n            if (glueMatch && glueMatch[1] && lastSeenTimestamp) {\n                console.log(\"V2: Found glue thickness: \".concat(glueMatch[1], \" at \").concat(lastSeenTimestamp));\n                glueThicknessData.push({\n                    timestamp: lastSeenTimestamp,\n                    value: parseFloat(glueMatch[1])\n                });\n            }\n            const v2CoordsMatch = line.match(_rulesEngine__WEBPACK_IMPORTED_MODULE_0__.REGEX_V2_COORDS);\n            if (v2CoordsMatch && lastSeenTimestamp) {\n                try {\n                    const x1 = parseFloat(v2CoordsMatch[1]);\n                    const y1 = parseFloat(v2CoordsMatch[2]);\n                    const x2 = parseFloat(v2CoordsMatch[3]);\n                    const y2 = parseFloat(v2CoordsMatch[4]);\n                    // Filter out invalid coordinates (large placeholder values)\n                    if (x1 < 100000 && y1 < 100000 && x2 < 100000 && y2 < 100000) {\n                        const diff = Math.sqrt(Math.pow(x1 - x2, 2) + Math.pow(y1 - y2, 2));\n                        if (Math.abs(diff) <= 100) {\n                            console.log(\"V2: Valid collimation diff: \".concat(diff, \" from coords (\").concat(x1, \", \").concat(y1, \") -> (\").concat(x2, \", \").concat(y2, \")\"));\n                            collimationData.push({\n                                timestamp: lastSeenTimestamp,\n                                value: diff\n                            });\n                        } else {\n                            console.log(\"V2: Collimation diff \".concat(diff, \" exceeds threshold of 100\"));\n                        }\n                    } else {\n                        console.log(\"V2: Filtered out invalid coords: x1=\".concat(x1, \", y1=\").concat(y1, \", x2=\").concat(x2, \", y2=\").concat(y2));\n                    }\n                } catch (e) {\n                    console.error(\"Error calculating V2 collimation diff:\", e);\n                }\n            }\n        }\n        if (version === 'V2') {\n            if (line.includes('轴已经停止')) {\n                console.log(\"V2: Exiting chart data section at line: \".concat(line));\n                inV2ChartDataSection = false;\n            }\n        }\n        if (line.includes('抽真空') && lastSeenTimestamp) {\n            vacuumPumpEvents.push({\n                timestamp: lastSeenTimestamp,\n                line_content: line,\n                type: 'vacuum'\n            });\n        }\n        if (line.includes('打开放气阀') && lastSeenTimestamp) {\n            ventValveEvents.push({\n                timestamp: lastSeenTimestamp,\n                line_content: line,\n                type: 'vent'\n            });\n        }\n    }\n    const blockId = generateBlockId(startTime);\n    const processedBlock = {\n        block_id: sn ? \"\".concat(blockId, \"_\").concat(sn) : blockId,\n        start_time: startTime,\n        end_time: endTime,\n        lines_count: blockLines.length,\n        vacuum_pump_events: vacuumPumpEvents,\n        vent_valve_events: ventValveEvents,\n        glue_thickness_values: glueThicknessData,\n        collimation_diff_values: collimationData,\n        sns: sn ? [\n            sn\n        ] : [],\n        raw_content: entireBlock,\n        version\n    };\n    return processedBlock;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./workers/dataExtractor.module.ts\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ /* webpack/runtime/getFullHash */
/******/ (() => {
/******/ 	__webpack_require__.h = () => ("75a853842717a226")
/******/ })();
/******/ 
/******/ }
);