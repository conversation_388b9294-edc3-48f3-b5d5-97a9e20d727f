import { processLogFile, findBlocksByTimestamp } from './logParser.module';

let processedLogBlocks: any[] = [];
/**
 * 定义发送到 Worker 的消息格式。
 */
// The message from the main thread is an object with `type` and `payload`
type WorkerMessage =
  | {
      type: 'PARSE_LOG';
      payload: {
        logContent: string;
      };
    }
  | {
      type: 'MATCH_BY_TIMESTAMP';
      payload: {
        timestamps: number[];
      };
    };

// 设置全局错误处理，以防有未捕获的异常
self.addEventListener('error', (event) => {
  console.error('Unhandled error in worker:', event);
  self.postMessage({
    type: 'ERROR',
    error: {
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
    },
  });
});

/**
 * 监听来自主线程的消息。
 */
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  switch (event.data.type) {
    case 'PARSE_LOG': {
      const { logContent } = event.data.payload;
      if (!logContent) {
        const errorMessage = {
          type: 'PARSE_LOG_RESULT',
          error: 'Invalid message format: logContent is missing in payload.',
        };
        console.error('[Worker] Posting error to main thread:', errorMessage);
        self.postMessage(errorMessage);
        return;
      }

      try {
        const processedData = await processLogFile(logContent);
        processedLogBlocks = processedData.blocks; // Store processed blocks
        const message = {
          type: 'PARSE_LOG_RESULT',
          allBlocks: processedData.blocks,
        };
        console.log('[Worker] Posting message to main thread:', message);
        self.postMessage(message);
      } catch (error) {
        console.error('Error processing log file in worker:', error);
        const errorMessage = {
          type: 'PARSE_LOG_RESULT',
          error:
            error instanceof Error
              ? error.message
              : 'An unknown error occurred.',
        };
        console.error('[Worker] Posting error to main thread:', errorMessage);
        self.postMessage(errorMessage);
      }
      break;
    }
    case 'MATCH_BY_TIMESTAMP': {
      const { timestamps } = event.data.payload;
      try {
        const matchedBlockIds = findBlocksByTimestamp(
          processedLogBlocks,
          timestamps
        );
        self.postMessage({
          type: 'MATCH_BY_TIMESTAMP_RESULT',
          payload: { matchedBlockIds },
        });
      } catch (error) {
        console.error('Error during timestamp match in worker:', error);
        self.postMessage({
          type: 'MATCH_BY_TIMESTAMP_RESULT',
          error: 'An error occurred during the search.',
        });
      }
      break;
    }
    default:
      console.error('Unknown message type:', (event.data as any).type);
      break;
  }
};

// eslint-disable-next-line no-console
console.log('Log parser worker initialized.');