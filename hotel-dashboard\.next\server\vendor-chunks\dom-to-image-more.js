/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/dom-to-image-more";
exports.ids = ["vendor-chunks/dom-to-image-more"];
exports.modules = {

/***/ "(ssr)/./node_modules/dom-to-image-more/dist/dom-to-image-more.min.js":
/*!**********************************************************************!*\
  !*** ./node_modules/dom-to-image-more/dist/dom-to-image-more.min.js ***!
  \**********************************************************************/
/***/ (function(module) {

eval("/*! dom-to-image-more 08-05-2025 */\n(l=>{let f=(()=>{let e=0;return{escape:function(e){return e.replace(/([.*+?^${}()|[\\]/\\\\])/g,\"\\\\$1\")},isDataUrl:function(e){return-1!==e.search(/^(data:)/)},canvasToBlob:function(t){if(t.toBlob)return new Promise(function(e){t.toBlob(e)});return(r=>new Promise(function(e){var t=u(r.toDataURL().split(\",\")[1]),n=t.length,o=new Uint8Array(n);for(let e=0;e<n;e++)o[e]=t.charCodeAt(e);e(new Blob([o],{type:\"image/png\"}))}))(t)},resolveUrl:function(e,t){var n=document.implementation.createHTMLDocument(),o=n.createElement(\"base\"),r=(n.head.appendChild(o),n.createElement(\"a\"));return n.body.appendChild(r),o.href=t,r.href=e,r.href},getAndEncode:function(s){let e=d.impl.urlCache.find(function(e){return e.url===s});e||(e={url:s,promise:null},d.impl.urlCache.push(e));null===e.promise&&(d.impl.options.cacheBust&&(s+=(/\\?/.test(s)?\"&\":\"?\")+(new Date).getTime()),e.promise=new Promise(function(t){let e=d.impl.options.httpTimeout,r=new XMLHttpRequest;if(r.onreadystatechange=function(){if(4===r.readyState)if(300<=r.status)n?t(n):l(`cannot fetch resource: ${s}, status: `+r.status);else{let e=new FileReader;e.onloadend=function(){t(e.result)},e.readAsDataURL(r.response)}},r.ontimeout=function(){n?t(n):l(`timeout of ${e}ms occured while fetching resource: `+s)},r.responseType=\"blob\",r.timeout=e,0<d.impl.options.useCredentialsFilters.length&&(d.impl.options.useCredentials=0<d.impl.options.useCredentialsFilters.filter(e=>0<=s.search(e)).length),d.impl.options.useCredentials&&(r.withCredentials=!0),d.impl.options.corsImg&&0===s.indexOf(\"http\")&&-1===s.indexOf(window.location.origin)){var i=\"POST\"===(d.impl.options.corsImg.method||\"GET\").toUpperCase()?\"POST\":\"GET\";r.open(i,(d.impl.options.corsImg.url||\"\").replace(\"#{cors}\",s),!0);let t=!1,n=d.impl.options.corsImg.headers||{},o=(Object.keys(n).forEach(function(e){-1!==n[e].indexOf(\"application/json\")&&(t=!0),r.setRequestHeader(e,n[e])}),(e=>{try{return JSON.parse(JSON.stringify(e))}catch(e){l(\"corsImg.data is missing or invalid:\"+e.toString())}})(d.impl.options.corsImg.data||\"\"));Object.keys(o).forEach(function(e){\"string\"==typeof o[e]&&(o[e]=o[e].replace(\"#{cors}\",s))}),r.send(t?JSON.stringify(o):o)}else r.open(\"GET\",s,!0),r.send();let n;function l(e){console.error(e),t(\"\")}d.impl.options.imagePlaceholder&&(i=d.impl.options.imagePlaceholder.split(/,/))&&i[1]&&(n=i[1])}));return e.promise},uid:function(){return\"u\"+(\"0000\"+(Math.random()*Math.pow(36,4)<<0).toString(36)).slice(-4)+e++},delay:function(n){return function(t){return new Promise(function(e){setTimeout(function(){e(t)},n)})}},asArray:function(t){var n=[],o=t.length;for(let e=0;e<o;e++)n.push(t[e]);return n},escapeXhtml:function(e){return e.replace(/%/g,\"%25\").replace(/#/g,\"%23\").replace(/\\n/g,\"%0A\")},makeImage:function(r){return\"data:,\"!==r?new Promise(function(e,t){let n=document.createElementNS(\"http://www.w3.org/2000/svg\",\"svg\"),o=new Image;d.impl.options.useCredentials&&(o.crossOrigin=\"use-credentials\"),o.onload=function(){document.body.removeChild(n),window&&window.requestAnimationFrame?window.requestAnimationFrame(function(){e(o)}):e(o)},o.onerror=e=>{document.body.removeChild(n),t(e)},n.appendChild(o),o.src=r,document.body.appendChild(n)}):Promise.resolve()},width:function(e){var t=i(e,\"width\");if(!isNaN(t))return t;var t=i(e,\"border-left-width\"),n=i(e,\"border-right-width\");return e.scrollWidth+t+n},height:function(e){var t=i(e,\"height\");if(!isNaN(t))return t;var t=i(e,\"border-top-width\"),n=i(e,\"border-bottom-width\");return e.scrollHeight+t+n},getWindow:t,isElement:r,isElementHostForOpenShadowRoot:function(e){return r(e)&&null!==e.shadowRoot},isShadowRoot:n,isInShadowRoot:o,isHTMLElement:function(e){return e instanceof t(e).HTMLElement},isHTMLCanvasElement:function(e){return e instanceof t(e).HTMLCanvasElement},isHTMLInputElement:function(e){return e instanceof t(e).HTMLInputElement},isHTMLImageElement:function(e){return e instanceof t(e).HTMLImageElement},isHTMLLinkElement:function(e){return e instanceof t(e).HTMLLinkElement},isHTMLScriptElement:function(e){return e instanceof t(e).HTMLScriptElement},isHTMLStyleElement:function(e){return e instanceof t(e).HTMLStyleElement},isHTMLTextAreaElement:function(e){return e instanceof t(e).HTMLTextAreaElement},isShadowSlotElement:function(e){return o(e)&&e instanceof t(e).HTMLSlotElement},isSVGElement:function(e){return e instanceof t(e).SVGElement},isSVGRectElement:function(e){return e instanceof t(e).SVGRectElement},isDimensionMissing:function(e){return isNaN(e)||e<=0}};function t(e){e=e?e.ownerDocument:void 0;return(e?e.defaultView:void 0)||window||l}function n(e){return e instanceof t(e).ShadowRoot}function o(e){return null!=e&&void 0!==e.getRootNode&&n(e.getRootNode())}function r(e){return e instanceof t(e).Element}function i(t,n){if(t.nodeType===m){let e=h(t).getPropertyValue(n);if(\"px\"===e.slice(-2))return e=e.slice(0,-2),parseFloat(e)}return NaN}})(),r=(()=>{let o=/url\\(([\"']?)((?:\\\\?.)*?)\\1\\)/gm;return{inlineAll:function(t,o,r){if(!e(t))return Promise.resolve(t);return Promise.resolve(t).then(n).then(function(e){let n=Promise.resolve(t);return e.forEach(function(t){n=n.then(function(e){return i(e,t,o,r)})}),n})},shouldProcess:e,impl:{readUrls:n,inline:i,urlAsRegex:r}};function e(e){return-1!==e.search(o)}function n(e){for(var t,n=[];null!==(t=o.exec(e));)n.push(t[2]);return n.filter(function(e){return!f.isDataUrl(e)})}function r(e){return new RegExp(`url\\\\(([\"']?)(${f.escape(e)})\\\\1\\\\)`,\"gm\")}function i(n,o,t,e){return Promise.resolve(o).then(function(e){return t?f.resolveUrl(e,t):e}).then(e||f.getAndEncode).then(function(e){var t=r(o);return n.replace(t,`url($1${e}$1)`)})}})(),e={resolveAll:function(){return t().then(function(e){return Promise.all(e.map(function(e){return e.resolve()}))}).then(function(e){return e.join(\"\\n\")})},impl:{readAll:t}};function t(){return Promise.resolve(f.asArray(document.styleSheets)).then(function(e){let n=[];return e.forEach(function(t){var e=Object.getPrototypeOf(t);if(Object.prototype.hasOwnProperty.call(e,\"cssRules\"))try{f.asArray(t.cssRules||[]).forEach(n.push.bind(n))}catch(e){console.error(\"domtoimage: Error while reading CSS rules from \"+t.href,e.toString())}}),n}).then(function(e){return e.filter(function(e){return e.type===CSSRule.FONT_FACE_RULE}).filter(function(e){return r.shouldProcess(e.style.getPropertyValue(\"src\"))})}).then(function(e){return e.map(t)});function t(t){return{resolve:function(){var e=(t.parentStyleSheet||{}).href;return r.inlineAll(t.cssText,e)},src:function(){return t.style.getPropertyValue(\"src\")}}}}let n={inlineAll:function t(e){if(!f.isElement(e))return Promise.resolve(e);return n(e).then(function(){return f.isHTMLImageElement(e)?o(e).inline():Promise.all(f.asArray(e.childNodes).map(function(e){return t(e)}))});function n(o){let e=[\"background\",\"background-image\"],t=e.map(function(t){let e=o.style.getPropertyValue(t),n=o.style.getPropertyPriority(t);return e?r.inlineAll(e).then(function(e){o.style.setProperty(t,e,n)}):Promise.resolve()});return Promise.all(t).then(function(){return o})}},impl:{newImage:o}};function o(n){return{inline:function(e){if(f.isDataUrl(n.src))return Promise.resolve();return Promise.resolve(n.src).then(e||f.getAndEncode).then(function(t){return new Promise(function(e){n.onload=e,n.onerror=e,n.src=t})})}}}let s={copyDefaultStyles:!0,imagePlaceholder:void 0,cacheBust:!1,useCredentials:!1,useCredentialsFilters:[],httpTimeout:3e4,styleCaching:\"strict\",corsImg:void 0,adjustClonedNode:void 0,filterStyles:void 0},d={toSvg:a,toPng:function(e,t){return i(e,t).then(function(e){return e.toDataURL()})},toJpeg:function(e,t){return i(e,t).then(function(e){return e.toDataURL(\"image/jpeg\",(t?t.quality:void 0)||1)})},toBlob:function(e,t){return i(e,t).then(f.canvasToBlob)},toPixelData:function(t,e){return i(t,e).then(function(e){return e.getContext(\"2d\").getImageData(0,0,f.width(t),f.height(t)).data})},toCanvas:i,impl:{fontFaces:e,images:n,util:f,inliner:r,urlCache:[],options:{}}},m=( true?module.exports=d:0,(\"undefined\"!=typeof Node?Node.ELEMENT_NODE:void 0)||1),h=(void 0!==l?l.getComputedStyle:void 0)||(\"undefined\"!=typeof window?window.getComputedStyle:void 0)||globalThis.getComputedStyle,u=(void 0!==l?l.atob:void 0)||(\"undefined\"!=typeof window?window.atob:void 0)||globalThis.atob;function a(e,r){let t=d.impl.util.getWindow(e);var n=r=r||{};void 0===n.copyDefaultStyles?d.impl.options.copyDefaultStyles=s.copyDefaultStyles:d.impl.options.copyDefaultStyles=n.copyDefaultStyles,d.impl.options.imagePlaceholder=(void 0===n.imagePlaceholder?s:n).imagePlaceholder,d.impl.options.cacheBust=(void 0===n.cacheBust?s:n).cacheBust,d.impl.options.corsImg=(void 0===n.corsImg?s:n).corsImg,d.impl.options.useCredentials=(void 0===n.useCredentials?s:n).useCredentials,d.impl.options.useCredentialsFilters=(void 0===n.useCredentialsFilters?s:n).useCredentialsFilters,d.impl.options.httpTimeout=(void 0===n.httpTimeout?s:n).httpTimeout,d.impl.options.styleCaching=(void 0===n.styleCaching?s:n).styleCaching;let i=[];return Promise.resolve(e).then(function(e){if(e.nodeType===m)return e;var t=e,n=e.parentNode,o=document.createElement(\"span\");return n.replaceChild(o,t),o.append(e),i.push({parent:n,child:t,wrapper:o}),o}).then(function(e){return function l(t,s,r,u){let e=s.filter;if(t===p||f.isHTMLScriptElement(t)||f.isHTMLStyleElement(t)||f.isHTMLLinkElement(t)||null!==r&&e&&!e(t))return Promise.resolve();return Promise.resolve(t).then(n).then(o).then(function(e){return c(e,a(t))}).then(i).then(function(e){return d(e,t)});function n(e){return f.isHTMLCanvasElement(e)?f.makeImage(e.toDataURL()):e.cloneNode(!1)}function o(e){return s.adjustClonedNode&&s.adjustClonedNode(t,e,!1),Promise.resolve(e)}function i(e){return s.adjustClonedNode&&s.adjustClonedNode(t,e,!0),Promise.resolve(e)}function a(e){return f.isElementHostForOpenShadowRoot(e)?e.shadowRoot:e}function c(n,e){let o=t(e),r=Promise.resolve();if(0!==o.length){let t=h(i(e));f.asArray(o).forEach(function(e){r=r.then(function(){return l(e,s,t,u).then(function(e){e&&n.appendChild(e)})})})}return r.then(function(){return n});function i(e){return f.isShadowRoot(e)?e.host:e}function t(t){if(f.isShadowSlotElement(t)){let e=t.assignedNodes();if(e&&0<e.length)return e}return t.childNodes}}function d(u,a){return!f.isElement(u)||f.isShadowSlotElement(a)?Promise.resolve(u):Promise.resolve().then(e).then(t).then(n).then(o).then(function(){return u});function e(){function o(e,t){t.font=e.font,t.fontFamily=e.fontFamily,t.fontFeatureSettings=e.fontFeatureSettings,t.fontKerning=e.fontKerning,t.fontSize=e.fontSize,t.fontStretch=e.fontStretch,t.fontStyle=e.fontStyle,t.fontVariant=e.fontVariant,t.fontVariantCaps=e.fontVariantCaps,t.fontVariantEastAsian=e.fontVariantEastAsian,t.fontVariantLigatures=e.fontVariantLigatures,t.fontVariantNumeric=e.fontVariantNumeric,t.fontVariationSettings=e.fontVariationSettings,t.fontWeight=e.fontWeight}function e(e,t){let n=h(e);n.cssText?(t.style.cssText=n.cssText,o(n,t.style)):(y(s,e,n,r,t),null===r&&([\"inset-block\",\"inset-block-start\",\"inset-block-end\"].forEach(e=>t.style.removeProperty(e)),[\"left\",\"right\",\"top\",\"bottom\"].forEach(e=>{t.style.getPropertyValue(e)&&t.style.setProperty(e,\"0px\")})))}e(a,u)}function t(){let s=f.uid();function t(r){let i=h(a,r),l=i.getPropertyValue(\"content\");if(\"\"!==l&&\"none\"!==l){let e=u.getAttribute(\"class\")||\"\",t=(u.setAttribute(\"class\",e+\" \"+s),document.createElement(\"style\"));function n(){let e=`.${s}:`+r,t=(i.cssText?n:o)();return document.createTextNode(e+`{${t}}`);function n(){return`${i.cssText} content: ${l};`}function o(){let e=f.asArray(i).map(t).join(\"; \");return e+\";\";function t(e){let t=i.getPropertyValue(e),n=i.getPropertyPriority(e)?\" !important\":\"\";return e+\": \"+t+n}}}t.appendChild(n()),u.appendChild(t)}}[\":before\",\":after\"].forEach(function(e){t(e)})}function n(){f.isHTMLTextAreaElement(a)&&(u.innerHTML=a.value),f.isHTMLInputElement(a)&&u.setAttribute(\"value\",a.value)}function o(){f.isSVGElement(u)&&(u.setAttribute(\"xmlns\",\"http://www.w3.org/2000/svg\"),f.isSVGRectElement(u))&&[\"width\",\"height\"].forEach(function(e){let t=u.getAttribute(e);t&&u.style.setProperty(e,t)})}}}(e,r,null,t)}).then(r.disableEmbedFonts?Promise.resolve(e):c).then(g).then(function(t){r.bgcolor&&(t.style.backgroundColor=r.bgcolor);r.width&&(t.style.width=r.width+\"px\");r.height&&(t.style.height=r.height+\"px\");r.style&&Object.keys(r.style).forEach(function(e){t.style[e]=r.style[e]});let e=null;\"function\"==typeof r.onclone&&(e=r.onclone(t));return Promise.resolve(e).then(function(){return t})}).then(function(e){let n=r.width||f.width(e),o=r.height||f.height(e);return Promise.resolve(e).then(function(e){return e.setAttribute(\"xmlns\",\"http://www.w3.org/1999/xhtml\"),(new XMLSerializer).serializeToString(e)}).then(f.escapeXhtml).then(function(e){var t=(f.isDimensionMissing(n)?' width=\"100%\"':` width=\"${n}\"`)+(f.isDimensionMissing(o)?' height=\"100%\"':` height=\"${o}\"`);return`<svg xmlns=\"http://www.w3.org/2000/svg\"${(f.isDimensionMissing(n)?\"\":` width=\"${n}\"`)+(f.isDimensionMissing(o)?\"\":` height=\"${o}\"`)}><foreignObject${t}>${e}</foreignObject></svg>`}).then(function(e){return\"data:image/svg+xml;charset=utf-8,\"+e})}).then(function(e){for(;0<i.length;){var t=i.pop();t.parent.replaceChild(t.child,t.wrapper)}return e}).then(function(e){return d.impl.urlCache=[],(()=>{p&&(document.body.removeChild(p),p=null),v&&clearTimeout(v),v=setTimeout(()=>{v=null,w={}},2e4)})(),e})}function i(r,i){return a(r,i=i||{}).then(f.makeImage).then(function(e){var t=\"number\"!=typeof i.scale?1:i.scale,n=((e,t)=>{let n=i.width||f.width(e),o=i.height||f.height(e);return f.isDimensionMissing(n)&&(n=f.isDimensionMissing(o)?300:2*o),f.isDimensionMissing(o)&&(o=n/2),(e=document.createElement(\"canvas\")).width=n*t,e.height=o*t,i.bgcolor&&((t=e.getContext(\"2d\")).fillStyle=i.bgcolor,t.fillRect(0,0,e.width,e.height)),e})(r,t),o=n.getContext(\"2d\");return o.msImageSmoothingEnabled=!1,o.imageSmoothingEnabled=!1,e&&(o.scale(t,t),o.drawImage(e,0,0)),n})}let p=null;function c(n){return e.resolveAll().then(function(e){var t;return\"\"!==e&&(t=document.createElement(\"style\"),n.appendChild(t),t.appendChild(document.createTextNode(e))),n})}function g(e){return n.inlineAll(e).then(function(){return e})}function y(i,l,s,u,e){let a=d.impl.options.copyDefaultStyles?((t,e)=>{var n,o=(e=>(\"relaxed\"!==t.styleCaching?e:e.filter((e,t,n)=>0===t||t===n.length-1)).join(\">\"))(e=(e=>{var t=[];do{if(e.nodeType===m){var n=e.tagName;if(t.push(n),E.includes(n))break}}while(e=e.parentNode);return t})(e));{if(w[o])return w[o];e=((e,t)=>{let n=e.body;do{var o=t.pop(),o=e.createElement(o);n.appendChild(o),n=o}while(0<t.length);return n.textContent=\"​\",n})((n=(()=>{if(p)return p.contentWindow;t=document.characterSet||\"UTF-8\",e=(e=document.doctype)?(`<!DOCTYPE ${s(e.name)} ${s(e.publicId)} `+s(e.systemId)).trim()+\">\":\"\",(p=document.createElement(\"iframe\")).id=\"domtoimage-sandbox-\"+f.uid(),p.style.visibility=\"hidden\",p.style.position=\"fixed\",document.body.appendChild(p);var e,t,n=p,o=\"domtoimage-sandbox\";try{return n.contentWindow.document.write(e+`<html><head><meta charset='${t}'><title>${o}</title></head><body></body></html>`),n.contentWindow}catch(e){}var r=document.createElement(\"meta\");r.setAttribute(\"charset\",t);try{var i=document.implementation.createHTMLDocument(o),l=(i.head.appendChild(r),e+i.documentElement.outerHTML);return n.setAttribute(\"srcdoc\",l),n.contentWindow}catch(e){}return n.contentDocument.head.appendChild(r),n.contentDocument.title=o,n.contentWindow;function s(e){var t;return e?((t=document.createElement(\"div\")).innerText=e,t.innerHTML):\"\"}})()).document,e),n=((e,t)=>{let n={},o=e.getComputedStyle(t);return f.asArray(o).forEach(function(e){n[e]=\"width\"===e||\"height\"===e?\"auto\":o.getPropertyValue(e)}),n})(n,e);var r=e;do{var i=r.parentElement;null!==i&&i.removeChild(r),r=i}while(r&&\"BODY\"!==r.tagName);return w[o]=n}})(i,l):{},c=e.style;f.asArray(s).forEach(function(e){var t,n,o,r;i.filterStyles&&!i.filterStyles(l,e)||(n=s.getPropertyValue(e),o=a[e],t=u?u.getPropertyValue(e):void 0,c.getPropertyValue(e))||(n!==o||u&&n!==t)&&(o=s.getPropertyPriority(e),t=c,n=n,o=o,r=0<=[\"background-clip\"].indexOf(e=e),o?(t.setProperty(e,n,o),r&&t.setProperty(\"-webkit-\"+e,n,o)):(t.setProperty(e,n),r&&t.setProperty(\"-webkit-\"+e,n)))})}let v=null,w={},E=[\"ADDRESS\",\"ARTICLE\",\"ASIDE\",\"BLOCKQUOTE\",\"DETAILS\",\"DIALOG\",\"DD\",\"DIV\",\"DL\",\"DT\",\"FIELDSET\",\"FIGCAPTION\",\"FIGURE\",\"FOOTER\",\"FORM\",\"H1\",\"H2\",\"H3\",\"H4\",\"H5\",\"H6\",\"HEADER\",\"HGROUP\",\"HR\",\"LI\",\"MAIN\",\"NAV\",\"OL\",\"P\",\"PRE\",\"SECTION\",\"SVG\",\"TABLE\",\"UL\",\"math\",\"svg\",\"BODY\",\"HEAD\",\"HTML\"]})(this);\n//# sourceMappingURL=dom-to-image-more.min.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/dom-to-image-more/dist/dom-to-image-more.min.js\n");

/***/ })

};
;