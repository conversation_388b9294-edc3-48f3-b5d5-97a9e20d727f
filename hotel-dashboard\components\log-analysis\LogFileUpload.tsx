"use client";

import React, { useState, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { decodeText } from "@/utils/fileEncoding";

/**
 * @interface LogFileUploadProps
 * @description `LogFileUpload` 组件的属性定义。
 */
interface LogFileUploadProps {
  /**
   * @property
   * @description 当文件成功读取并解码后调用的回调函数。
   * @param {string} logContent - 解码后的日志文件文本内容。
   * @returns {void}
   */
  onFileProcessed: (logContent: string) => void;
  /**
   * @property
   * @description 是否禁用组件的交互，通常在父组件处理其他任务时设置为 true。
   * @type {boolean}
   * @default false
   */
  disabled?: boolean;
  /**
   * @property
   * @description 指示当前是否正在处理文件。当为 true 时，按钮将显示“处理中...”并被禁用。
   * @type {boolean}
   * @default false
   */
  isProcessing?: boolean;
}

/**
 * @component LogFileUpload
 * @description 一个用于上传和初步处理日志文件的 React 组件。
 *
 * @state
 * @property {File | null} selectedFile - 存储用户通过文件输入框选择的当前文件对象。初始值为 null。
 *
 * @logic
 * 该组件负责以下核心逻辑：
 * 1. **文件选择**: 允许用户通过一个自定义样式的文件输入框选择本地的日志文件（.log, .txt 等）。
 * 2. **状态管理**: 使用 `useState` (`selectedFile`) 来跟踪用户选择的文件。UI 会根据 `selectedFile` 的状态（是否存在）来更新显示的文件名和按钮的可用性。
 * 3. **文件读取与解码**:
 *    - 当用户点击“上传并分析”按钮时，`handleUploadClick` 函数被触发。
 *    - 它使用 `FileReader` API 以 `ArrayBuffer` 的形式异步读取文件内容。这对于后续的编码检测至关重要。
 *    - 读取完成后，调用从 `@/utils/fileEncoding` 导入的 `decodeText` 辅助函数。该函数负责检测文件的实际字符编码（如 UTF-8, GBK）并将 `ArrayBuffer` 解码为正确的 JavaScript 字符串。
 * 4. **与父组件通信**:
 *    - 文件成功解码后，组件通过调用从 props 传入的 `onFileProcessed` 函数，将解码后的字符串内容向上传递给父组件。
 *    - 父组件（如此处的日志分析页面）接收到内容后，通常会将其发送给 Web Worker 进行后续的密集型解析任务，从而避免阻塞 UI 线程。
 * 5. **UI 反馈**:
 *    - 通过 `disabled` 和 `isProcessing` props 控制组件的禁用状态和加载状态。
 *    - 使用 `toast` 提供用户反馈，例如在未选择文件、文件读取失败或解码错误时显示提示信息。
 *
 * @example
 * ```tsx
 * <LogFileUpload
 *   onFileProcessed={(content) => console.log(content)}
 *   isProcessing={isLoading}
 *   disabled={isBusy}
 * />
 * ```
 */
const LogFileUpload: React.FC<LogFileUploadProps> = ({
  onFileProcessed,
  disabled = false,
  isProcessing = false,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setSelectedFiles(Array.from(event.target.files));
    } else {
      setSelectedFiles([]);
    }
  };

  const handleUploadClick = useCallback(async () => {
    if (selectedFiles.length === 0) {
      toast({
        title: "没有选择文件",
        description: "请选择一个或多个日志文件进行上传。",
        variant: "destructive",
      });
      return;
    }

    try {
      const fileContents = await Promise.all(
        selectedFiles.map(file => {
          return new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
              try {
                const buffer = e.target?.result as ArrayBuffer;
                const content = decodeText(buffer);
                resolve(content);
              } catch (error) {
                reject(error);
              }
            };
            reader.onerror = () => {
              reject(new Error(`读取文件 ${file.name} 时发生错误。`));
            };
            reader.readAsArrayBuffer(file);
          });
        })
      );

      const combinedContent = fileContents.join('\n\n');
      console.log('所有文件编码检测和解码完成');
      onFileProcessed(combinedContent);

    } catch (error) {
      console.error('文件处理失败:', error);
      toast({
        title: "文件处理错误",
        description: error instanceof Error ? error.message : "无法正确处理一个或多个文件。",
        variant: "destructive",
      });
    }
  }, [selectedFiles, onFileProcessed]);

  const totalSize = selectedFiles.reduce((acc, file) => acc + file.size, 0);

  return (
    <Card className="w-full max-w-md h-[350px] flex flex-col">
      <CardHeader>
        <CardTitle>日志文件上传</CardTitle>
        <CardDescription>选择一个或多个日志文件进行分析。</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6 flex-grow flex flex-col overflow-y-auto">
        <div className="flex flex-col items-start gap-1.5">
          <Label htmlFor="hidden-log-file" className="text-sm font-medium">选择文件</Label>
          <label
            htmlFor="hidden-log-file"
            className={`w-full h-10 rounded-md border border-input flex items-center text-sm ring-offset-background ${disabled || isProcessing ? 'cursor-not-allowed bg-muted' : 'cursor-pointer focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2'}`}
          >
            <span className={`bg-primary text-primary-foreground h-full flex items-center justify-center px-4 rounded-l-md whitespace-nowrap ${disabled || isProcessing ? 'opacity-50' : ''}`}>
              上传文件
            </span>
            <span className="flex-grow h-full px-3 py-2 flex items-center text-muted-foreground overflow-hidden">
              <span className="truncate">
                {selectedFiles.length > 0
                  ? `${selectedFiles.length} 个文件已选择`
                  : "未选择文件"}
              </span>
            </span>
          </label>
          <input
            id="hidden-log-file"
            type="file"
            className="sr-only"
            onChange={handleFileChange}
            disabled={disabled || isProcessing}
            multiple
          />
        </div>
        {selectedFiles.length > 0 && (
          <div className="space-y-2 text-sm pt-3">
            <div className="text-muted-foreground">
              <p>总大小: {(totalSize / 1024).toFixed(2)} KB</p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleUploadClick} disabled={selectedFiles.length === 0 || disabled || isProcessing} className="w-full">
          {isProcessing ? '处理中...' : '上传并分析'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default LogFileUpload;