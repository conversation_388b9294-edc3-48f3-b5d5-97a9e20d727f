"use client";

import React, { useState, useCallback } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";

interface LogFileUploadProps {
  onFileProcessed: (logContent: string) => void;
  disabled?: boolean;
  isProcessing?: boolean;
}

const LogFileUpload: React.FC<LogFileUploadProps> = ({
  onFileProcessed,
  disabled = false,
  isProcessing = false,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files.length > 0) {
      setSelectedFile(event.target.files[0]);
    } else {
      setSelectedFile(null);
    }
  };

  const handleUploadClick = useCallback(() => {
    if (!selectedFile) {
      toast({
        title: "没有选择文件",
        description: "请选择一个日志文件进行上传。",
        variant: "destructive",
      });
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      onFileProcessed(content);
    };
    reader.onerror = () => {
      toast({
        title: "文件读取错误",
        description: "读取文件时发生错误。",
        variant: "destructive",
      });
    };
    reader.readAsText(selectedFile, 'GBK');
  }, [selectedFile, onFileProcessed]);

  return (
    <Card className="w-full max-w-md h-[350px] flex flex-col">
      <CardHeader>
        <CardTitle>日志文件上传</CardTitle>
        <CardDescription>选择一个日志文件进行分析。</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6 p-6 flex-grow flex flex-col overflow-y-auto">
        <div className="flex flex-col items-start gap-1.5">
          <Label htmlFor="hidden-log-file" className="text-sm font-medium">选择文件</Label>
          <label
            htmlFor="hidden-log-file"
            className={`w-full h-10 rounded-md border border-input flex items-center text-sm ring-offset-background ${disabled || isProcessing ? 'cursor-not-allowed bg-muted' : 'cursor-pointer focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2'}`}
          >
            <span className={`bg-primary text-primary-foreground h-full flex items-center justify-center px-4 rounded-l-md whitespace-nowrap ${disabled || isProcessing ? 'opacity-50' : ''}`}>
              上传文件
            </span>
            <span className="flex-grow h-full px-3 py-2 flex items-center text-muted-foreground overflow-hidden">
              <span className="truncate">
                {selectedFile ? selectedFile.name : "未选择文件"}
              </span>
            </span>
          </label>
          <input
            id="hidden-log-file"
            type="file"
            className="sr-only"
            onChange={handleFileChange}
            disabled={disabled || isProcessing}
            accept=".log,.txt,.1,.2,.3,application/octet-stream"
          />
        </div>
        {selectedFile && (
          <div className="space-y-2 text-sm pt-3">
            <div className="text-muted-foreground">
              <p>大小: {(selectedFile.size / 1024).toFixed(2)} KB</p>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={handleUploadClick} disabled={!selectedFile || disabled || isProcessing} className="w-full">
          {isProcessing ? '处理中...' : '上传并分析'}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default LogFileUpload;