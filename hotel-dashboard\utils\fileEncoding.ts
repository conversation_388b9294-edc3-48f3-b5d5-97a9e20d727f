/**
 * @file 文件编码处理工具
 * @description 该模块提供了一系列函数，用于检测和处理不同编码格式的文件，特别是针对包含中文的日志文件（如UTF-8, GBK, GB2312等）。
 */

/**
 * 智能解码文本内容，自动识别并处理多种文件编码。
 *
 * 该函数通过一系列策略来尝试解码一个ArrayBuffer，以获得正确的文本内容。
 * 处理流程如下：
 * 1.  **BOM (Byte Order Mark) 检测**: 首先检查文件头是否存在UTF-8, UTF-16LE, 或 UTF-16BE的BOM。如果存在，则使用对应的编码进行解码，这是最准确的方式。
 * 2.  **启发式解码 (Heuristic Decoding)**: 如果没有BOM，函数会按顺序尝试一系列编码格式 (`['utf-8', 'gbk', 'gb18030', 'gb2312', 'big5']`)。
 *     - 它首先尝试UTF-8。
 *     - 如果UTF-8解码失败或解码后的内容不包含特定的业务关键词（如“开始抽真空”、“胶厚值”等），它会继续尝试其他中文编码。
 *     - 这种基于关键词的验证方法是一种启发式策略，用于判断解码是否“有意义”。
 * 3.  **回退 (Fallback)**: 如果所有尝试都失败，函数将默认使用UTF-8进行解码，并返回结果。这是一种尽力而为的策略，以防所有检测都失效。
 *
 * @param buffer - 包含原始文件二进制数据的 `ArrayBuffer` 对象。
 * @returns {string} 解码后的文本字符串。
 */
export function decodeText(buffer: ArrayBuffer): string {
  // 首先尝试检测BOM标记
  const uint8Array = new Uint8Array(buffer);
  
  // 检查UTF-8 BOM
  if (uint8Array.length >= 3 && uint8Array[0] === 0xEF && uint8Array[1] === 0xBB && uint8Array[2] === 0xBF) {
    console.log('检测到UTF-8 BOM标记');
    return new TextDecoder('utf-8').decode(buffer.slice(3));
  }
  
  // 检查UTF-16LE BOM
  if (uint8Array.length >= 2 && uint8Array[0] === 0xFF && uint8Array[1] === 0xFE) {
    console.log('检测到UTF-16LE BOM标记');
    return new TextDecoder('utf-16le').decode(buffer.slice(2));
  }
  
  // 检查UTF-16BE BOM
  if (uint8Array.length >= 2 && uint8Array[0] === 0xFE && uint8Array[1] === 0xFF) {
    console.log('检测到UTF-16BE BOM标记');
    return new TextDecoder('utf-16be').decode(buffer.slice(2));
  }
  
  // 尝试不同的编码
  const encodings = ['utf-8', 'gbk', 'gb18030', 'gb2312', 'big5'];
  
  // 首先尝试UTF-8
  try {
    const utf8Text = new TextDecoder('utf-8').decode(buffer);
    
    // 检查UTF-8解码是否有效 - 如果包含中文关键词则认为解码成功
    if (utf8Text.includes('开始抽真空') || utf8Text.includes('胶厚值') ||
        utf8Text.includes('轴停止运动') || utf8Text.includes('轴已经停止')) {
      console.log('成功使用UTF-8解码');
      return utf8Text;
    }
  } catch (e) {
    console.error('UTF-8解码失败:', e);
  }
  
  // 如果浏览器支持GBK/GB2312解码
  if (typeof TextDecoder !== 'undefined') {
    for (const encoding of encodings) {
      if (encoding === 'utf-8') continue; // 已经尝试过
      
      try {
        // 注意：某些浏览器可能不支持所有这些编码
        const decoder = new TextDecoder(encoding, { fatal: false });
        const text = decoder.decode(buffer);
        
        // 检查解码是否有效 - 如果包含中文关键词则认为解码成功
        if (text.includes('开始抽真空') || text.includes('胶厚值') ||
            text.includes('轴停止运动') || text.includes('轴已经停止')) {
          console.log(`成功使用${encoding}解码`);
          return text;
        }
      } catch (e) {
        console.error(`${encoding}解码失败:`, e);
      }
    }
  }
  
  // 如果所有编码都失败，回退到UTF-8
  console.warn('无法确定正确的编码，回退到UTF-8');
  return new TextDecoder('utf-8').decode(buffer);
}

/**
 * 检查一个字符串是否包含有效的中文内容。
 *
 * 此函数用于辅助判断解码后的文本是否正确。它通过两种方式进行检查：
 * 1.  **关键词检查**: 检查字符串是否包含一系列预定义的、与业务逻辑相关的中文关键词。
 * 2.  **通用中文字符检查**: 如果关键词检查未通过，则使用正则表达式检查是否存在任何Unicode中文字符。
 *
 * @param text - 需要被检查的文本字符串。
 * @returns {boolean} 如果字符串被认为是包含有效的中文内容，则返回 `true`，否则返回 `false`。
 */
export function containsValidChinese(text: string): boolean {
  // 检查是否包含常见的中文关键词
  const keywords = ['开始抽真空', '胶厚值', '轴停止运动', '轴已经停止', '点13均值'];
  
  for (const keyword of keywords) {
    if (text.includes(keyword)) {
      return true;
    }
  }
  
  // 检查是否包含中文字符范围
  const chineseRegex = /[\u4e00-\u9fa5]/;
  return chineseRegex.test(text);
}
