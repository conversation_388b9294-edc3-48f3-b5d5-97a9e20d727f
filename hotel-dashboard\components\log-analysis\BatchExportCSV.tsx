"use client";

import { Button } from "@/components/ui/button";
import { ProcessedBlock } from "@/workers/logParser.definitions";
import { extractAndParseSqlInserts, exportDataToCsv } from "@/lib/exportUtils";
import { toast } from "sonner";

interface BatchExportCSVProps {
  selectedBlocks: ProcessedBlock[];
}

const BatchExportCSV = ({ selectedBlocks }: BatchExportCSVProps) => {
  const isDisabled = selectedBlocks.length === 0;

  const handleExport = () => {
    if (isDisabled) return;

    try {
      console.log("Starting CSV export for selected blocks:", selectedBlocks);
      const sqlData = extractAndParseSqlInserts(selectedBlocks);

      if (sqlData.length === 0) {
        toast.warning("No 'INSERT INTO g_support' statements found in the selected blocks.");
        return;
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `g_support_export_${timestamp}`;
      exportDataToCsv(sqlData, filename);
      toast.success(`Successfully exported ${sqlData.length} records to ${filename}.csv`);
    } catch (error) {
      console.error("Failed to export CSV:", error);
      toast.error("An unexpected error occurred during CSV export.");
    }
  };

  return (
    <Button onClick={handleExport} disabled={isDisabled}>
      Batch Export to CSV ({selectedBlocks.length})
    </Button>
  );
};

export default BatchExportCSV;