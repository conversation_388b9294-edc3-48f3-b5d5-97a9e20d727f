// hotel-dashboard/workers/logParser.definitions.ts

export interface TimestampedValue {
  timestamp: string;
  value: number | null; // Allow null for values
}

export interface SpecialEvent {
  timestamp: string;
  line_content: string;
  type: 'vacuum' | 'vent';
}

export interface ProcessedBlock {
  block_id: string;
  start_time: string | null;
  end_time: string | null;
  lines_count: number;
  vacuum_pump_events: SpecialEvent[];
  vent_valve_events: SpecialEvent[];
  glue_thickness_values: TimestampedValue[];
  collimation_diff_values: TimestampedValue[];
  sns: string[];
  raw_content: string; // Add raw content for further processing
  version: 'V1' | 'V2' | 'UNKNOWN';
  preVentCollimatorDiff: number | null;
  collimatorDiffAfterUV: number | null;
}

export interface FormattedDataPoint {
  timestamp: string;
  glue_thickness: number | null;
  collimation_diff: number | null;
}

export interface FormattedBlockData {
  block_id: string;
  data_points: FormattedDataPoint[];
  start_time: string | null;
  end_time: string | null;
  vacuum_pump_events: SpecialEvent[];
  vent_valve_events: SpecialEvent[];
}

export function formatBlockDataForFrontend(blockData: ProcessedBlock): FormattedBlockData | null {
    if (!blockData) return null;

    const glueValues = blockData.glue_thickness_values || [];
    const collimValues = blockData.collimation_diff_values || [];

    const glueMap = new Map(glueValues.map(item => [item.timestamp, item.value]));
    const collimMap = new Map(collimValues.map(item => [item.timestamp, item.value]));

    const timestampSet: { [key: string]: boolean } = {};
    for (const item of glueValues) {
        if (item.timestamp) timestampSet[item.timestamp] = true;
    }
    for (const item of collimValues) {
        if (item.timestamp) timestampSet[item.timestamp] = true;
    }
    const allTimestamps = Object.keys(timestampSet);
    allTimestamps.sort();

    const dataPoints: FormattedDataPoint[] = allTimestamps.map(ts => ({
        timestamp: ts,
        glue_thickness: glueMap.get(ts) ?? null,
        collimation_diff: collimMap.get(ts) ?? null,
    })).filter(dp => dp.timestamp);
    
    return {
        block_id: blockData.block_id || "N/A",
        data_points: dataPoints,
        start_time: blockData.start_time,
        end_time: blockData.end_time,
        vacuum_pump_events: blockData.vacuum_pump_events || [],
        vent_valve_events: blockData.vent_valve_events || [],
    };
}

export interface DataBlock {
  id: string;
  version: 'V1' | 'V2' | 'UNKNOWN';
  content: string;
  sn?: string;
}

export interface ProcessedLogResults {
  blocks: ProcessedBlock[];
  totalLines: number;
  v1Count: number;
  v2Count: number;
  unknownCount: number;
}