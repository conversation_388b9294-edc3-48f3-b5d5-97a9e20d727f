#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import math

def parse_v2_log(file_path):
    """解析V2格式的日志文件"""
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    lines = content.split('\n')
    print(f"总行数: {len(lines)}")
    
    # 查找数据块边界
    start_line = -1
    end_line = -1
    
    for i, line in enumerate(lines):
        if '开始抽真空' in line:
            start_line = i
            print(f"找到开始标记在第 {i+1} 行: {line}")
        if 'insert into g_support' in line:
            end_line = i
            print(f"找到结束标记在第 {i+1} 行: {line[:100]}...")
    
    if start_line == -1 or end_line == -1:
        print("未找到完整的数据块边界")
        return None, None, None
    
    block_lines = lines[start_line:end_line+1]
    print(f"数据块包含 {len(block_lines)} 行")
    
    # 查找数据收集区间
    data_start = -1
    data_end = -1
    
    for i, line in enumerate(block_lines):
        if '轴停止运动' in line:
            data_start = i
            print(f"找到数据收集开始标记在块内第 {i+1} 行: {line}")
        if '轴已经停止' in line:
            data_end = i
            print(f"找到数据收集结束标记在块内第 {i+1} 行: {line}")
    
    if data_start == -1 or data_end == -1:
        print("未找到数据收集区间标记")
        return None, None, None
    
    # 提取数据收集区间的行
    data_lines = block_lines[data_start:data_end+1]
    print(f"数据收集区间包含 {len(data_lines)} 行")
    
    # 正则表达式
    timestamp_regex = r'(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2},\d{3})'
    thickness_regex = r'INFO\s+.*Thickness:([-\d.]+)'
    coords_regex = r'点13均值x:([-\d.]+),\s*点13均值y:([-\d.]+),\s*点2x:([-\d.]+),\s*点2y:([-\d.]+)'
    
    timestamps = []
    thickness_data = []
    collimation_data = []
    
    last_timestamp = None
    
    for line in data_lines:
        # 提取时间戳
        ts_match = re.search(timestamp_regex, line)
        if ts_match:
            last_timestamp = ts_match.group(1)
        
        # 提取胶厚数据
        thickness_match = re.search(thickness_regex, line)
        if thickness_match and last_timestamp:
            thickness_value = float(thickness_match.group(1))
            if thickness_value > 0:  # 过滤掉0值
                thickness_data.append((last_timestamp, thickness_value))
                print(f"胶厚数据: {thickness_value} at {last_timestamp}")
        
        # 提取坐标数据并计算准直差值
        coords_match = re.search(coords_regex, line)
        if coords_match and last_timestamp:
            x1 = float(coords_match.group(1))
            y1 = float(coords_match.group(2))
            x2 = float(coords_match.group(3))
            y2 = float(coords_match.group(4))
            
            # 过滤掉无效坐标（大数值）
            if x1 < 100000 and y1 < 100000 and x2 < 100000 and y2 < 100000:
                diff = np.sqrt((x1 - x2)**2 + (y1 - y2)**2)
                if abs(diff) <= 100:  # 差值阈值
                    collimation_data.append((last_timestamp, diff))
                    print(f"准直数据: {diff:.6f} from ({x1}, {y1}) -> ({x2}, {y2}) at {last_timestamp}")
                else:
                    print(f"准直差值超出阈值: {diff}")
            else:
                print(f"过滤无效坐标: x1={x1}, y1={y1}, x2={x2}, y2={y2}")
    
    print(f"\n提取结果:")
    print(f"胶厚数据点: {len(thickness_data)}")
    print(f"准直数据点: {len(collimation_data)}")
    
    return thickness_data, collimation_data, data_lines

def plot_data(thickness_data, collimation_data):
    """绘制数据图表"""
    
    if not thickness_data and not collimation_data:
        print("没有数据可以绘制")
        return
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 绘制胶厚数据
    if thickness_data:
        times = [datetime.strptime(ts.replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f') for ts, _ in thickness_data]
        values = [val for _, val in thickness_data]
        
        ax1.plot(times, values, 'b-', linewidth=1, label='胶厚值')
        ax1.set_ylabel('胶厚值')
        ax1.set_title(f'V2格式日志数据 - 胶厚值 ({len(thickness_data)} 个数据点)')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        
        # 格式化x轴时间显示
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        ax1.xaxis.set_major_locator(mdates.SecondLocator(interval=30))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    else:
        ax1.text(0.5, 0.5, '没有胶厚数据', ha='center', va='center', transform=ax1.transAxes)
        ax1.set_title('V2格式日志数据 - 胶厚值 (0 个数据点)')
    
    # 绘制准直数据
    if collimation_data:
        times = [datetime.strptime(ts.replace(',', '.'), '%Y-%m-%d %H:%M:%S.%f') for ts, _ in collimation_data]
        values = [val for _, val in collimation_data]
        
        ax2.plot(times, values, 'r-', linewidth=1, label='准直差值')
        ax2.set_ylabel('准直差值')
        ax2.set_xlabel('时间')
        ax2.set_title(f'V2格式日志数据 - 准直差值 ({len(collimation_data)} 个数据点)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        
        # 格式化x轴时间显示
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%H:%M:%S'))
        ax2.xaxis.set_major_locator(mdates.SecondLocator(interval=30))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    else:
        ax2.text(0.5, 0.5, '没有准直数据', ha='center', va='center', transform=ax2.transAxes)
        ax2.set_title('V2格式日志数据 - 准直差值 (0 个数据点)')
    
    plt.tight_layout()
    plt.savefig('v2_log_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    print("=== V2日志解析和绘图 ===")
    
    file_path = 'v2.txt'
    thickness_data, collimation_data, data_lines = parse_v2_log(file_path)
    
    if thickness_data is not None:
        plot_data(thickness_data, collimation_data)
        
        # 显示一些样本数据
        if thickness_data:
            print(f"\n胶厚数据样本 (前5个):")
            for i, (ts, val) in enumerate(thickness_data[:5]):
                print(f"  {i+1}. {ts}: {val}")
        
        if collimation_data:
            print(f"\n准直数据样本 (前5个):")
            for i, (ts, val) in enumerate(collimation_data[:5]):
                print(f"  {i+1}. {ts}: {val:.6f}")
    else:
        print("解析失败")

if __name__ == "__main__":
    main()
