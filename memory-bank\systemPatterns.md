# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-07-07 18:29:39 - Log of updates made.

*

## Coding Patterns

*   

## Architectural Patterns

*   [2025-07-14 11:53:00] - **Web Worker Offloading for Intensive Parsing:** For CPU-intensive tasks like log file parsing and data extraction, a Web Worker is employed. This pattern prevents the main UI thread from being blocked, ensuring the application remains responsive. The main thread communicates with the worker via a message-based protocol, sending the raw log content and receiving structured data back. This is crucial for handling large log files without degrading the user experience in the `hotel-dashboard` application.

## Testing Patterns

*