import domtoimage from 'dom-to-image-more';
import { saveAs } from 'file-saver';
import J<PERSON><PERSON><PERSON> from 'jszip';
import ExcelJS from 'exceljs';
import { ProcessedBlock } from '@/workers/logParser.definitions';

// Define the structure for the parsed SQL insert data
export interface SqlInsertData {
  sn: string;
  image_name: string;
  result: string;
  timestamp: string;
  [key: string]: any; // Allow other dynamic properties
}

/**
 * Generates a Blob from a single DOM element.
 * @param element The HTML element to convert to an image.
 * @returns A Promise that resolves with the image Blob.
 */
export async function generateSingleImageBlob(element: HTMLElement): Promise<Blob> {
  try {
    const dataUrl = await domtoimage.toPng(element, {
      width: element.scrollWidth,
      height: element.scrollHeight,
      bgcolor: '#ffffff',
      style: {
        'border': 'none !important',
        'outline': 'none !important',
        'box-shadow': 'none !important',
        'background-color': '#ffffff !important'
      },
      filter: (node: Node) => {
        if (node instanceof HTMLElement) {
          // Ensure styles are reset for capture
          node.style.border = 'none';
          node.style.outline = 'none';
          node.style.boxShadow = 'none';
        }
        return true;
      },
      cacheBust: true
    });

    const res = await fetch(dataUrl);
    const blob = await res.blob();
    if (!blob) {
      throw new Error('Failed to convert data URL to Blob.');
    }
    return blob;
  } catch (error) {
    console.error('Error generating image blob:', error);
    throw new Error('Failed to generate image from element.');
  }
}

/**
 * Zips an array of images (as Blobs) and triggers a download.
 * @param images An array of objects, each with a filename and a Blob.
 * @param zipFilename The desired name for the output ZIP file.
 */
export async function zipAndDownloadImages(
  images: { filename: string; blob: Blob }[],
  zipFilename:string
): Promise<void> {
  try {
    const zip = new JSZip();

    images.forEach(({ filename, blob }) => {
      zip.file(filename, blob);
    });

    const content = await zip.generateAsync({ type: 'blob' });
    
    if (content.size === 0) {
      throw new Error('Generated zip file is empty. This might happen if all image generations failed.');
    }

    saveAs(content, `${zipFilename}.zip`);
  } catch (error) {
    console.error('Error zipping and downloading images:', error);
    throw new Error('Failed to create or download the zip file.');
  }
}

/**
 * Waits for a Recharts chart to be fully rendered within a container.
 * It polls the container to check for the presence of a '.recharts-surface' element
 * and ensures that a "loading" message is not present.
 * @param container The HTML element that contains the chart.
 * @param timeout The maximum time to wait in milliseconds.
 * @returns A Promise that resolves when the chart is ready, or rejects on timeout.
 */
export async function waitForChartReady(container: HTMLElement, timeout: number = 10000): Promise<void> {
  const startTime = Date.now();
  return new Promise((resolve, reject) => {
    const check = () => {
      // Check for the SVG chart surface rendered by Recharts
      const hasChart = container.querySelector('.recharts-surface');
      // Check if the container or its children are displaying a loading text
      const isLoading = container.innerText.includes("图表加载中...");

      if (hasChart && !isLoading) {
        // Chart is ready
        resolve();
      } else if (Date.now() - startTime > timeout) {
        // Timeout exceeded
        reject(new Error("Waiting for chart to render timed out."));
      } else {
        // Wait and check again
        setTimeout(check, 300);
      }
    };
    check();
  });
}

/**
 * Extracts and parses "INSERT INTO g_support" statements from raw log content.
 * @param blocks An array of ProcessedBlock objects.
 * @returns An array of parsed data objects.
 */
export function extractAndParseSqlInserts(blocks: ProcessedBlock[]): SqlInsertData[] {
  const allInserts: SqlInsertData[] = [];
  const sqlRegex = /INSERT INTO g_support \((.*?)\) VALUES \((.*?)\);/g;

  for (const block of blocks) {
    if (!block.raw_content) continue;

    let match;
    while ((match = sqlRegex.exec(block.raw_content)) !== null) {
      const keys = match[1].split(',').map(k => k.trim().replace(/`/g, ''));
      const values = match[2].split(',').map(v => v.trim().replace(/'/g, ''));

      const data: { [key: string]: any } = {};
      keys.forEach((key, index) => {
        data[key] = values[index] || '';
      });

      // Ensure required fields are present
      if (data.sn && data.image_name && data.result && data.timestamp) {
        allInserts.push({
          sn: data.sn,
          image_name: data.image_name,
          result: data.result,
          timestamp: data.timestamp,
          ...data, // Include any other fields
        });
      }
    }
  }

  return allInserts;
}

/**
 * Converts an array of objects to a CSV string and triggers a download.
 * @param data The array of data objects to export.
 * @param filename The desired name for the output CSV file.
 */
export function exportDataToCsv(data: SqlInsertData[], filename: string): void {
  if (data.length === 0) {
    console.warn('No data provided to export.');
    return;
  }

  try {
    // Dynamically create headers from all unique keys in the data
    const allKeys = data.reduce((keys, item) => {
      Object.keys(item).forEach((key: string) => {
        if (!keys.includes(key)) {
          keys.push(key);
        }
      });
      return keys;
    }, [] as string[]);

    const csvRows: string[] = [];
    // Add header row
    csvRows.push(allKeys.join(','));

    // Add data rows
    for (const item of data) {
      const values = allKeys.map((key: string) => {
        const value = item[key] !== null && item[key] !== undefined ? String(item[key]) : '';
        // Escape commas and quotes
        const escaped = value.includes(',') || value.includes('"') ? `"${value.replace(/"/g, '""')}"` : value;
        return escaped;
      });
      csvRows.push(values.join(','));
    }

    const csvString = csvRows.join('\n');
    const blob = new Blob([`\uFEFF${csvString}`], { type: 'text/csv;charset=utf-8;' });

    saveAs(blob, `${filename}.csv`);
  } catch (error) {
    console.error('Error exporting data to CSV:', error);
    throw new Error('Failed to create or download the CSV file.');
  }
}

interface ExportRow {
  "Timestamp": string;
  "SN": string;
  "破真空前准直": any;
  "终值": any;
  "OQC准直"?: any; // New field for OQC value
  "Chart"?: string; // Base64 string
}

export async function exportSnDataToXlsx(data: ExportRow[], fileName: string): Promise<void> {
  console.log(`[ExcelJS Export] exportSnDataToXlsx called with ${data.length} rows.`);
  if (!data || data.length === 0) {
    console.warn('[ExcelJS Export] No data provided for XLSX export. Aborting.');
    return;
  }

  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Log Data');

  // 1. Define headers and set column widths
  worksheet.columns = [
    { header: 'Timestamp', key: 'Timestamp', width: 25 },
    { header: 'SN', key: 'SN', width: 30 },
    { header: '破真空前准直', key: '破真空前准直', width: 18 },
    { header: '终值', key: '终值', width: 18 },
    { header: 'OQC准直', key: 'OQC准直', width: 18 }, // New column header
    { header: 'Chart', key: 'Chart', width: 45 },
  ];

  // 2. Add data rows and images
  for (const [index, rowData] of data.entries()) {
    const row = worksheet.addRow({
      'Timestamp': rowData.Timestamp,
      'SN': rowData.SN,
      '破真空前准直': rowData['破真空前准直'],
      '终值': rowData['终值'],
      'OQC准直': rowData['OQC准直'], // Add new data point to the row
    });

    if (rowData.Chart && rowData.Chart.startsWith('data:image/png;base64,')) {
      const base64Image = rowData.Chart.split(',')[1];
      
      const imageId = workbook.addImage({
        base64: base64Image,
        extension: 'png',
      });

      // Place the image in the 'Chart' column (6th column now)
      worksheet.addImage(imageId, {
        tl: { col: 5, row: index + 1 }, // col is 0-based, row is 1-based
        ext: { width: 300, height: 225 }
      });
      
      // Set row height to accommodate the image
      row.height = 175;
    }
  }

  // 3. Generate buffer and trigger download
  try {
    const buffer = await workbook.xlsx.writeBuffer();
    saveAs(new Blob([buffer]), `${fileName}.xlsx`);
    console.log(`[ExcelJS Export] Successfully triggered download for ${fileName}.xlsx.`);
  } catch (error) {
    console.error('[ExcelJS Export] Error writing or saving XLSX file:', error);
    // Optionally, notify the user that the export failed
  }
}