import domtoimage from 'dom-to-image-more';
import { saveAs } from 'file-saver';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import { ProcessedBlock } from '@/workers/logParser.definitions';

// Define the structure for the parsed SQL insert data
export interface SqlInsertData {
  sn: string;
  image_name: string;
  result: string;
  timestamp: string;
  [key: string]: any; // Allow other dynamic properties
}

/**
 * Generates a Blob from a single DOM element.
 * @param element The HTML element to convert to an image.
 * @returns A Promise that resolves with the image Blob.
 */
export async function generateSingleImageBlob(element: HTMLElement): Promise<Blob> {
  try {
    const dataUrl = await domtoimage.toPng(element, {
      width: element.scrollWidth,
      height: element.scrollHeight,
      bgcolor: '#ffffff',
      style: {
        'border': 'none !important',
        'outline': 'none !important',
        'box-shadow': 'none !important',
        'background-color': '#ffffff !important'
      },
      filter: (node: Node) => {
        if (node instanceof HTMLElement) {
          // Ensure styles are reset for capture
          node.style.border = 'none';
          node.style.outline = 'none';
          node.style.boxShadow = 'none';
        }
        return true;
      },
      cacheBust: true
    });

    const res = await fetch(dataUrl);
    const blob = await res.blob();
    if (!blob) {
      throw new Error('Failed to convert data URL to Blob.');
    }
    return blob;
  } catch (error) {
    console.error('Error generating image blob:', error);
    throw new Error('Failed to generate image from element.');
  }
}

/**
 * Zips an array of images (as Blobs) and triggers a download.
 * @param images An array of objects, each with a filename and a Blob.
 * @param zipFilename The desired name for the output ZIP file.
 */
export async function zipAndDownloadImages(
  images: { filename: string; blob: Blob }[],
  zipFilename:string
): Promise<void> {
  try {
    const zip = new JSZip();

    images.forEach(({ filename, blob }) => {
      zip.file(filename, blob);
    });

    const content = await zip.generateAsync({ type: 'blob' });
    
    if (content.size === 0) {
      throw new Error('Generated zip file is empty. This might happen if all image generations failed.');
    }

    saveAs(content, `${zipFilename}.zip`);
  } catch (error) {
    console.error('Error zipping and downloading images:', error);
    throw new Error('Failed to create or download the zip file.');
  }
}

/**
 * Waits for a Recharts chart to be fully rendered within a container.
 * It polls the container to check for the presence of a '.recharts-surface' element
 * and ensures that a "loading" message is not present.
 * @param container The HTML element that contains the chart.
 * @param timeout The maximum time to wait in milliseconds.
 * @returns A Promise that resolves when the chart is ready, or rejects on timeout.
 */
export async function waitForChartReady(container: HTMLElement, timeout: number = 10000): Promise<void> {
  const startTime = Date.now();
  return new Promise((resolve, reject) => {
    const check = () => {
      // Check for the SVG chart surface rendered by Recharts
      const hasChart = container.querySelector('.recharts-surface');
      // Check if the container or its children are displaying a loading text
      const isLoading = container.innerText.includes("图表加载中...");

      if (hasChart && !isLoading) {
        // Chart is ready
        resolve();
      } else if (Date.now() - startTime > timeout) {
        // Timeout exceeded
        reject(new Error("Waiting for chart to render timed out."));
      } else {
        // Wait and check again
        setTimeout(check, 300);
      }
    };
    check();
  });
}

/**
 * Extracts and parses "INSERT INTO g_support" statements from raw log content.
 * @param blocks An array of ProcessedBlock objects.
 * @returns An array of parsed data objects.
 */
export function extractAndParseSqlInserts(blocks: ProcessedBlock[]): SqlInsertData[] {
  const allInserts: SqlInsertData[] = [];
  const sqlRegex = /INSERT INTO g_support \((.*?)\) VALUES \((.*?)\);/g;

  for (const block of blocks) {
    if (!block.raw_content) continue;

    let match;
    while ((match = sqlRegex.exec(block.raw_content)) !== null) {
      const keys = match[1].split(',').map(k => k.trim().replace(/`/g, ''));
      const values = match[2].split(',').map(v => v.trim().replace(/'/g, ''));

      const data: { [key: string]: any } = {};
      keys.forEach((key, index) => {
        data[key] = values[index] || '';
      });

      // Ensure required fields are present
      if (data.sn && data.image_name && data.result && data.timestamp) {
        allInserts.push({
          sn: data.sn,
          image_name: data.image_name,
          result: data.result,
          timestamp: data.timestamp,
          ...data, // Include any other fields
        });
      }
    }
  }

  return allInserts;
}

/**
 * Converts an array of objects to a CSV string and triggers a download.
 * @param data The array of data objects to export.
 * @param filename The desired name for the output CSV file.
 */
export function exportDataToCsv(data: SqlInsertData[], filename: string): void {
  if (data.length === 0) {
    console.warn('No data provided to export.');
    return;
  }

  try {
    // Dynamically create headers from all unique keys in the data
    const allKeys = data.reduce((keys, item) => {
      Object.keys(item).forEach((key: string) => {
        if (!keys.includes(key)) {
          keys.push(key);
        }
      });
      return keys;
    }, [] as string[]);

    const csvRows: string[] = [];
    // Add header row
    csvRows.push(allKeys.join(','));

    // Add data rows
    for (const item of data) {
      const values = allKeys.map((key: string) => {
        const value = item[key] !== null && item[key] !== undefined ? String(item[key]) : '';
        // Escape commas and quotes
        const escaped = value.includes(',') || value.includes('"') ? `"${value.replace(/"/g, '""')}"` : value;
        return escaped;
      });
      csvRows.push(values.join(','));
    }

    const csvString = csvRows.join('\n');
    const blob = new Blob([`\uFEFF${csvString}`], { type: 'text/csv;charset=utf-8;' });

    saveAs(blob, `${filename}.csv`);
  } catch (error) {
    console.error('Error exporting data to CSV:', error);
    throw new Error('Failed to create or download the CSV file.');
  }
}