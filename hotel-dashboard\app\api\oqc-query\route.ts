import { NextRequest, NextResponse } from 'next/server';
import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';

// Define the allowed tables and their corresponding OQC tables to prevent malicious queries
const ALLOWED_TABLES_MAP: Record<string, string> = {
  'g_support': 'g_oqc_zhunzhi',
  'h_support': 'h_oqc_zhunzhi'
};

export async function POST(request: NextRequest) {
  let filePath: string | null = null;

  try {
    // 1. Safely receive and parse the request body
    const body = await request.json();
    const { tableName, sn } = body;

    // 2. Validate parameters
    if (!tableName || !sn) {
      return NextResponse.json({ error: 'Missing tableName or sn parameter' }, { status: 400 });
    }

    // Security check: ensure the requested table is allowed
    const oqcTableName = ALLOWED_TABLES_MAP[tableName];
    if (!oqcTableName) {
      return NextResponse.json({ error: 'Invalid table name' }, { status: 403 });
    }

    // 3. Build the database query
    const sqlQuery = `SELECT diff FROM ${oqcTableName} WHERE sn = '${sn}' LIMIT 1`;

    // 4. Prepare to execute the external C# application
    const wrapperAppDir = path.resolve(process.cwd(), '..', 'csharp-wrapper', 'bin', 'Debug', 'net48');
    const wrapperAppPath = path.join(wrapperAppDir, 'WrapperApp.exe');
    const tempDir = process.env.TEMP || process.env.TMP || 'C:\\Windows\\Temp';
    // Use the correct fixed filename that the C# application writes to.
    filePath = path.join(tempDir, `db_query_result.json`);

    // Delete the file beforehand to ensure no stale data is read.
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }

    // 5. Execute the query safely
    const escapedQuery = sqlQuery.replace(/"/g, '\\"');
    const powershellCommand = `& "${wrapperAppPath}" "${escapedQuery}" --silent 2>$null`;

    await new Promise<void>((resolve, reject) => {
      const child = spawn('powershell', ['-Command', powershellCommand], {
        cwd: wrapperAppDir,
        stdio: 'pipe'
      });

      child.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Process exited with code ${code}`));
        } else {
          resolve();
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });

    // 6. Process the query result
    if (!fs.existsSync(filePath)) {
      return NextResponse.json({ diff: null });
    }

    const jsonContent = fs.readFileSync(filePath, 'utf8');
    const resultObject = JSON.parse(jsonContent);

    // The C# wrapper returns an object like { "Table1": [...] }
    const dataArray = resultObject.Table1;

    let diffValue = null;
    if (Array.isArray(dataArray) && dataArray.length > 0) {
      diffValue = dataArray[0]?.diff ?? null;
    }

    return NextResponse.json({ diff: diffValue });

  } catch (error) {
    console.error('API /api/oqc-query Error:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  } finally {
    // 7. Clean up the temporary file
    if (filePath && fs.existsSync(filePath)) {
      try {
        fs.unlinkSync(filePath);
      } catch (cleanupError) {
        console.error('Error cleaning up file:', cleanupError);
      }
    }
  }
}