"use client";

import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface ImageNameSearchProps {
  onSearch: (timestamps: number[]) => void;
  disabled?: boolean;
  isLoading?: boolean;
}

const ImageNameSearch = ({ onSearch, disabled, isLoading }: ImageNameSearchProps) => {
  const [inputText, setInputText] = useState("");
  const [error, setError] = useState<string | null>(null);

  const handleSearch = () => {
    setError(null);
    const fileNames = inputText.split("\n").filter((name) => name.trim() !== "");
    if (fileNames.length === 0) {
      setError("Please enter at least one file name.");
      return;
    }

    const timestamps: number[] = [];
    let hasError = false;

    fileNames.forEach((fileName) => {
      // New regex to match YYYYMMDD_HH_mm_ss format
      const match = fileName.match(/^(\d{8})_(\d{2})_(\d{2})_(\d{2})/);
      if (match) {
        const [, datePart, hourPart, minutePart, secondPart] = match;
        const year = parseInt(datePart.substring(0, 4), 10);
        const month = parseInt(datePart.substring(4, 6), 10) - 1; // Month is 0-indexed
        const day = parseInt(datePart.substring(6, 8), 10);
        const hour = parseInt(hourPart, 10);
        const minute = parseInt(minutePart, 10);
        const second = parseInt(secondPart, 10);

        const date = new Date(year, month, day, hour, minute, second);
        
        if (!isNaN(date.getTime())) {
          timestamps.push(Math.floor(date.getTime() / 1000));
        } else {
          hasError = true;
        }
      } else {
        hasError = true;
      }
    });

    if (hasError) {
      setError("Some file names have an incorrect format. Please use 'YYYYMMDD_HH_mm_ssGBSNxxxxxx.png' and ensure each is on a new line.");
    }
    
    if (timestamps.length > 0) {
        onSearch(timestamps);
    } else if (!hasError) {
        setError("No valid timestamps could be extracted. Please check the format.");
    }
  };

  return (
    <div className="space-y-4">
      <div className="grid w-full gap-1.5">
        <Label htmlFor="image-names">Image File Names</Label>
        <Textarea
          id="image-names"
          placeholder="Paste image file names here, one per line..."
          value={inputText}
          onChange={(e) => setInputText(e.target.value)}
          rows={5}
          disabled={disabled || isLoading}
        />
        <p className="text-sm text-muted-foreground">
          Example: `20230101_12_00_00GBSN123456.png`
        </p>
      </div>
      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      <Button onClick={handleSearch} disabled={disabled || isLoading}>
        {isLoading ? "Searching..." : "Search by Image Names"}
      </Button>
    </div>
  );
};

export default ImageNameSearch;