/**
 * @fileoverview
 * This file contains utility functions for processing and visualizing point cloud data.
 * It includes functions for parsing raw point cloud data from text format,
 * calculating the geometric bounds of the point cloud, and mapping data values
 * to a color gradient for visualization purposes.
 */

/**
 * Represents a 3D point with x, y, and z coordinates.
 */
export interface PointXYZ {
  /** The x-coordinate of the point. */
  x: number;
  /** The y-coordinate of the point. */
  y: number;
  /** The z-coordinate of the point, often used to represent height or intensity. */
  z: number;
}

/**
 * Calculates an RGB color from a predefined gradient based on a normalized input value.
 * The gradient transitions from Blue -> Cyan -> Green -> Yellow -> Red.
 * This function is a direct port of the `GetColorFromGradient` method from the original C# application (Form1.cs).
 *
 * @param normalizedValue - A value between 0 and 1, where 0 maps to blue and 1 maps to red. Values outside this range will be clamped.
 * @returns An object containing the RGB color components, each from 0 to 255.
 */
export function getColorFromGradient(normalizedValue: number): { r: number; g: number; b: number } {
  // Clamp the value to the [0, 1] range.
  if (normalizedValue < 0) normalizedValue = 0;
  if (normalizedValue > 1) normalizedValue = 1;

  if (normalizedValue < 0.25) {
    // Blue to Cyan
    const b = 255;
    const g = Math.round(255 * (normalizedValue * 4));
    return { r: 0, g, b };
  } else if (normalizedValue < 0.5) {
    // Cyan to Green
    const b = Math.round(255 * (1 - (normalizedValue - 0.25) * 4));
    const g = 255;
    return { r: 0, g, b };
  } else if (normalizedValue < 0.75) {
    // Green to Yellow
    const r = Math.round(255 * ((normalizedValue - 0.5) * 4));
    const g = 255;
    return { r, g, b: 0 };
  } else {
    // Yellow to Red
    const g = Math.round(255 * (1 - (normalizedValue - 0.75) * 4));
    const r = 255;
    return { r, g, b: 0 };
  }
}

/**
 * Parses a raw string of point cloud data into an array of PointXYZ objects.
 * The input string is expected to have one point per line, with x, y, and z values
 * separated by whitespace.
 *
 * To handle very large datasets efficiently, this function supports optional down-sampling.
 * If `maxPoints` is specified and the total number of points exceeds it, the function
 * will systematically sample the data to reduce the point count to the approximate target.
 *
 * @param data - The raw point cloud data as a string.
 * @param options - Optional parameters for parsing.
 * @param options.maxPoints - The maximum number of points to return. If specified, enables down-sampling.
 * @returns An array of parsed PointXYZ objects.
 */
export function parsePointCloudData(
  data: string,
  options?: { maxPoints?: number }
): PointXYZ[] {
  const points: PointXYZ[] = [];
  const lines = data.trim().split('\n');
  const totalPoints = lines.length;
  const maxPoints = options?.maxPoints;

  // Determine if sampling is needed and calculate the step size.
  const shouldUseSampling = maxPoints && totalPoints > maxPoints;
  const step = shouldUseSampling ? Math.floor(totalPoints / maxPoints) : 1;

  for (let i = 0; i < totalPoints; i += step) {
    const line = lines[i];
    if (!line) continue;

    const parts = line.trim().split(/\s+/);
    if (parts.length === 3) {
      const x = parseFloat(parts[0]);
      const y = parseFloat(parts[1]);
      const z = parseFloat(parts[2]);
      if (!isNaN(x) && !isNaN(y) && !isNaN(z)) {
        points.push({ x, y, z });
      }
    }
  }
  return points;
}

/**
 * Calculates the axis-aligned bounding box of a set of 3D points.
 * This is useful for determining the spatial extent of the point cloud,
 * which can be used for camera setup, normalization, or other calculations.
 *
 * @param points - An array of PointXYZ objects.
 * @returns An object containing the minimum and maximum coordinates for each axis (minX, maxX, minY, maxY, minZ, maxZ),
 * or null if the input array is empty or invalid.
 */
export function calculateBounds(points: PointXYZ[]): {
  minX: number;
  maxX: number;
  minY: number;
  maxY: number;
  minZ: number;
  maxZ: number;
} | null {
  if (!points || points.length === 0) {
    return null;
  }

  let minX = points[0].x;
  let maxX = points[0].x;
  let minY = points[0].y;
  let maxY = points[0].y;
  let minZ = points[0].z;
  let maxZ = points[0].z;

  for (const point of points) {
    if (point.x < minX) minX = point.x;
    if (point.x > maxX) maxX = point.x;
    if (point.y < minY) minY = point.y;
    if (point.y > maxY) maxY = point.y;
    if (point.z < minZ) minZ = point.z;
    if (point.z > maxZ) maxZ = point.z;
  }

  return { minX, maxX, minY, maxY, minZ, maxZ };
}