"use client";

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface SnSearchBarProps {
  onSearch: (query: string) => void;
  onClear: () => void;
  isLoading?: boolean;
}

export const SnSearchBar = ({ onSearch, onClear, isLoading = false }: SnSearchBarProps) => {
  const [query, setQuery] = useState('');

  const handleSearch = () => {
    onSearch(query);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      handleSearch();
    }
  };

  const handleClear = () => {
    setQuery('');
    onClear();
  };

  return (
    <div className="flex w-full max-w-lg items-center space-x-2">
      <Input
        type="text"
        placeholder="输入SN，用逗号、分号或空格分隔"
        value={query}
        onChange={(e) => setQuery(e.target.value)}
        onKeyDown={handleKeyDown}
        disabled={isLoading}
        className="flex-grow"
      />
      <Button onClick={handleSearch} disabled={isLoading}>
        搜索
      </Button>
      <Button variant="ghost" size="icon" onClick={handleClear} disabled={isLoading}>
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
};